# Rock Types Tree Testing Guide

This document outlines the testing procedures for the Rock Types Tree management UI to ensure all functionality works correctly before deployment to production.

## Setup

1. Ensure you have the latest code from the repository
2. Run the development server with `npm run dev`
3. Navigate to the Rock Types Tree page at `/rock-types-tree`
4. Make sure you're logged in with an account that has appropriate permissions

## Functionality Testing

### Navigation & Basic UI

- [ ] Verify that the Rock Types Tree page loads correctly
- [ ] Check that the breadcrumbs navigation works properly
- [ ] Confirm that switching between table view and tree view in rock type management works
- [ ] Test responsiveness on different screen sizes (desktop, tablet, mobile)

### Tree Display & Navigation

- [ ] Verify that tree roots (classification systems) load correctly in the dropdown
- [ ] Test selecting different tree roots from the dropdown
- [ ] Check that folders and rock types display with appropriate icons
- [ ] Test expanding and collapsing nodes using the arrow icons
- [ ] Confirm that the search functionality works for finding nodes
- [ ] Test the "Expand All" and "Collapse All" buttons
- [ ] Verify that inactive nodes are visually distinguishable (slightly transparent)
- [ ] Check that rock types with children show a warning indicator icon

### Node Creation

- [ ] Test creating a new folder at the root level
- [ ] Create a new rock type at the root level
- [ ] Create a folder as a child of an existing folder
- [ ] Create a rock type as a child of a folder
- [ ] Verify color picker works when selecting display colors
- [ ] Test validation in all form fields
- [ ] Check that the rock type selector dropdown search works correctly

### Node Editing

- [ ] Edit an existing folder (name, description, color, active state)
- [ ] Edit an existing rock type (name, code, description, color, active state)
- [ ] Verify that rock type field is disabled when editing an existing rock type node
- [ ] Confirm changes persist after saving and reloading the page

### Node Deletion

- [ ] Delete a leaf node (no children) and confirm deletion
- [ ] Attempt to delete a node with children and verify the warning message appears
- [ ] Confirm that node hierarchy is preserved after deletion operations

### Drag and Drop

- [ ] Drag a folder into another folder
- [ ] Move a rock type into a different folder
- [ ] Move a node to root level
- [ ] Test that the confirmation dialog appears when moving nodes
- [ ] Verify the tree updates correctly after moves
- [ ] Try invalid operations (if any should be prevented)

### Keyboard Navigation

- [ ] Test navigating the tree with arrow keys
- [ ] Use Enter key to expand/collapse nodes
- [ ] Try Delete key to delete the selected node
- [ ] Use Ctrl/Cmd+E shortcut to edit the selected node
- [ ] Check that focus indicators are visible for accessibility

### Error Handling

- [ ] Test error handling with network issues (simulate offline)
- [ ] Try operations with insufficient permissions
- [ ] Verify appropriate error messages are shown for failed operations
- [ ] Check validation error messages in forms

### Performance Testing

- [ ] Load a large tree (50+ nodes) and test performance
- [ ] Test search performance on large trees
- [ ] Check expand/collapse performance with many child nodes
- [ ] Verify that scrolling through a large tree is smooth

### Accessibility Testing

- [ ] Test with a screen reader to ensure all elements are properly announced
- [ ] Verify keyboard navigation works for all operations
- [ ] Check color contrast for all UI elements
- [ ] Test focus management throughout the interface
- [ ] Verify that all interactive elements have appropriate aria attributes

## Integration Testing

- [ ] Test navigation between traditional table view and tree view
- [ ] Verify that changes made in the tree view appear in the table view
- [ ] Check that permissions are correctly applied to both views
- [ ] Test that the sidebar navigation correctly highlights the current page

## Regression Testing

- [ ] Verify no regressions in existing rock type management functionality
- [ ] Check that other modules using rock types still work correctly
- [ ] Test the overall application navigation with the new tree page added

## Bug Reporting

If you encounter any issues during testing, please report them with:

1. Steps to reproduce
2. Expected behavior
3. Actual behavior
4. Screenshots (if applicable)
5. Browser/device information

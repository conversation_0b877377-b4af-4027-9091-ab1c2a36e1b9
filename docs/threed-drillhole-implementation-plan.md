# 3D Drilling Visualization Implementation Plan

After analyzing the existing code in both the vanilla Three.js implementation (3d folder) and the current React-based component, I've created a comprehensive plan for implementing an enhanced 3D visualization module using React Three Next.

## 1. Overview of Current Implementations

### Vanilla Three.js Implementation (3d/ folder)

- Implements a feature-rich 3D visualization for drillhole data
- Key features:
  - Interactive camera controls (orbit, pan, zoom)
  - Dynamic cross-section views (East-West and North-South)
  - Toggleable drillhole traces
  - Topographic surface visualization
  - Geology tube rendering with different colors
  - Dark/Light theme support
  - Filterable drillhole table
  - Legend for geology types

### Current React Implementation (src/app/3d-viewing/)

- Uses basic Three.js within React components
- Has a simpler visualization without geology data or cross-sections
- Uses Redux for state management
- Provides UI controls to add/remove drillholes

## 2. Folder Structure Based on Image Module Pattern

Following the structure of the image module, we will implement the 3D drillhole visualization with this folder organization:

```
/src/modules/threed/
  ├── api/
  │   └── threed.api.ts
  ├── components/
  │   ├── 3d-view.tsx (already exists)
  │   ├── drillhole-view-panel.tsx
  │   ├── drillhole-view.tsx
  │   ├── filter-drillhole.tsx
  │   ├── drillhole-list-view.tsx
  │   ├── drillhole-table.tsx
  │   ├── control-panel.tsx
  │   ├── legend-panel.tsx
  │   ├── tab-view.tsx
  │   └── canvas/
  │       ├── drillhole-model.tsx
  │       ├── geology-model.tsx
  │       ├── topography.tsx
  │       ├── section-plane.tsx
  │       ├── drillhole-label.tsx
  │       └── zoomable-scene.tsx
  ├── const/
  │   └── drillhole-view.const.ts
  ├── constants/
  │   └── threed.constant.ts
  ├── helpers/
  │   ├── threed.helpers.ts
  │   ├── drillhole.helper.ts
  │   └── geology.helper.ts
  ├── hooks/
  │   ├── useDataLoader.ts
  │   ├── useGetListDrillhole.hook.tsx
  │   ├── useGetGeologyData.hook.tsx
  │   ├── useGeologyColors.ts
  │   ├── useCameraControls.ts
  │   └── useUpdateDrillhole.hook.tsx
  ├── interface/
  │   └── threed.interface.ts
  ├── model/
  │   ├── entities/
  │   │   └── drillhole.config.ts
  │   ├── enum/
  │   │   ├── drillhole.enum.ts
  │   │   └── geology.enum.ts
  │   └── schema/
  │       └── threed.schema.ts
  └── redux/
      └── threedSlice/
          ├── threed.slice.ts
          ├── index.ts
          ├── selectors.ts
          └── thunks.ts
```

2. **Core Dependencies**
   - `react-three-next`: Main React Three.js reconciler
   - `@react-three/drei`: Useful helpers for React Three Next
   - `three`: Underlying Three.js library
   - `@reduxjs/toolkit`: For state management and async actions
   - `react-redux`: For React-Redux bindings
   - `react-use-gesture`: For advanced interactions

## 3. Technical Implementation

### API and Data Management

The data management approach will follow the image module pattern with dedicated API and Redux implementation:

```typescript
// src/modules/threed/api/threed.api.ts
import http from "@/lib/http";
import { appRequest } from "@/common/configs/app.di-container";
import { RequestState } from "@/common/configs/app.contants";
import { getErrorMessage } from "@/utils/error.utils";
import { DrillholeData, GeologyData } from "../interface/threed.interface";

const threedRequest = {
  getDrillholes: async () => {
    try {
      // Mock API response - using the CSV structure format
      // HOLEID,PROJECTCODE,EAST,NORTH,RL,DEPTH
      const mockDrillholes: DrillholeData[] = [
        {
          id: "AR1653-024",
          projectCode: "EXPLORE",
          east: 232740,
          north: 176540,
          rl: 1094.488,
          depth: 545.6,
        },
        {
          id: "AR1653-025",
          projectCode: "EXPLORE",
          east: 232760,
          north: 176580,
          rl: 1092.35,
          depth: 498.2,
        },
      ];

      return {
        state: RequestState.success,
        data: mockDrillholes,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  getGeologyData: async (drillholeId: string) => {
    try {
      // Mock API response - in real implementation this would fetch from API
      const mockGeologyData: GeologyData[] = [
        {
          drillholeId,
          segments: [
            { from: 0, to: 10, rockType: "SOIL", color: "#8B4513" },
            { from: 10, to: 20, rockType: "SDST", color: "#FFD700" },
            { from: 20, to: 30, rockType: "GRNT", color: "#A9A9A9" },
          ],
        },
      ];

      return {
        state: RequestState.success,
        data:
          mockGeologyData.find((g) => g.drillholeId === drillholeId)
            ?.segments || [],
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  // Additional API methods can be added here
};

export default threedRequest;
```

### Redux Implementation

```typescript
// src/modules/threed/redux/threedSlice/thunks.ts
import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import threedRequest from "../../api/threed.api";

export const getDrillholes = createAppAsyncThunk(
  "threed/getDrillholes",
  async () => {
    const response = await threedRequest.getDrillholes();
    return response;
  }
);

export const getGeologyData = createAppAsyncThunk(
  "threed/getGeologyData",
  async (drillholeId: string) => {
    const response = await threedRequest.getGeologyData(drillholeId);
    return response;
  }
);
```

```typescript
// src/modules/threed/redux/threedSlice/threed.slice.ts
import { RequestState } from "@/common/configs/app.contants";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getDrillholes, getGeologyData } from "./thunks";
import { DrillholeData, GeologyData } from "../../interface/threed.interface";

interface ThreedState {
  drillholes: {
    data: DrillholeData[];
    status: RequestState;
    error?: string;
  };
  geologyData: {
    [drillholeId: string]: {
      data: GeologyData[];
      status: RequestState;
      error?: string;
    };
  };
  selectedDrillholes: string[];
  visibilityMap: Record<string, boolean>;
  camera: {
    position: [number, number, number];
    target: [number, number, number];
  };
  showLabels: boolean;
  showLegend: boolean;
  sectionConfig: {
    enabled: boolean;
    direction: "ew" | "ns";
    position: number; // 0-100%
  };
}

const initialState: ThreedState = {
  drillholes: {
    data: [],
    status: RequestState.idle,
  },
  geologyData: {},
  selectedDrillholes: [],
  visibilityMap: {},
  camera: {
    position: [10, 10, 10],
    target: [0, 0, 0],
  },
  showLabels: true,
  showLegend: true,
  sectionConfig: {
    enabled: false,
    direction: "ew",
    position: 50,
  },
};

const threedSlice = createSlice({
  name: "threedSlice",
  initialState,
  reducers: {
    toggleDrillholeVisibility(
      state,
      action: PayloadAction<{ id: string; visible: boolean }>
    ) {
      const { id, visible } = action.payload;
      state.visibilityMap[id] = visible;
    },
    updateCameraPosition(
      state,
      action: PayloadAction<{
        position: [number, number, number];
        target: [number, number, number];
      }>
    ) {
      state.camera = action.payload;
    },
    toggleSection(
      state,
      action: PayloadAction<{
        enabled: boolean;
        direction?: "ew" | "ns";
        position?: number;
      }>
    ) {
      const { enabled, direction, position } = action.payload;
      state.sectionConfig.enabled = enabled;
      if (direction) state.sectionConfig.direction = direction;
      if (position !== undefined) state.sectionConfig.position = position;
    },
    selectDrillhole(state, action: PayloadAction<string>) {
      if (!state.selectedDrillholes.includes(action.payload)) {
        state.selectedDrillholes.push(action.payload);
      }
    },
    deselectDrillhole(state, action: PayloadAction<string>) {
      state.selectedDrillholes = state.selectedDrillholes.filter(
        (id) => id !== action.payload
      );
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle getDrillholes states
      .addCase(getDrillholes.pending, (state) => {
        state.drillholes.status = RequestState.pending;
      })
      .addCase(getDrillholes.fulfilled, (state, action) => {
        if (action.payload.state === RequestState.success) {
          state.drillholes.status = RequestState.success;
          state.drillholes.data = action.payload.data;
        } else {
          state.drillholes.status = RequestState.error;
          state.drillholes.error = action.payload.message;
        }
      })
      .addCase(getDrillholes.rejected, (state, action) => {
        state.drillholes.status = RequestState.error;
        state.drillholes.error = action.error.message;
      })

      // Handle getGeologyData states
      .addCase(getGeologyData.pending, (state, action) => {
        const drillholeId = action.meta.arg;
        state.geologyData[drillholeId] = {
          data: [],
          status: RequestState.pending,
        };
      })
      .addCase(getGeologyData.fulfilled, (state, action) => {
        const drillholeId = action.meta.arg;
        if (action.payload.state === RequestState.success) {
          state.geologyData[drillholeId] = {
            data: action.payload.data,
            status: RequestState.success,
          };
        } else {
          state.geologyData[drillholeId] = {
            data: [],
            status: RequestState.error,
            error: action.payload.message,
          };
        }
      })
      .addCase(getGeologyData.rejected, (state, action) => {
        const drillholeId = action.meta.arg;
        state.geologyData[drillholeId] = {
          data: [],
          status: RequestState.error,
          error: action.error.message,
        };
      });
  },
});

export const {
  toggleDrillholeVisibility,
  updateCameraPosition,
  toggleSection,
  selectDrillhole,
  deselectDrillhole,
} = threedSlice.actions;
export default threedSlice.reducer;
```

```typescript
// src/modules/threed/redux/threedSlice/selectors.ts
import { ReduxState } from "@/common/vendors/redux/store/store";

export const selectDrillholes = (state: ReduxState) => state.threed.drillholes;
export const selectGeologyData = (drillholeId: string) => (state: ReduxState) =>
  state.threed.geologyData[drillholeId];
export const selectSelectedDrillholes = (state: ReduxState) =>
  state.threed.selectedDrillholes;
export const selectVisibilityMap = (state: ReduxState) =>
  state.threed.visibilityMap;
export const selectCamera = (state: ReduxState) => state.threed.camera;
export const selectSectionConfig = (state: ReduxState) =>
  state.threed.sectionConfig;
export const selectShowLabels = (state: ReduxState) => state.threed.showLabels;
export const selectShowLegend = (state: ReduxState) => state.threed.showLegend;
```

### Custom Hooks

```typescript
// src/modules/threed/hooks/useGetListDrillhole.hook.tsx
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RequestState } from "@/common/configs/app.contants";
import { DrillholeData } from "../interface/threed.interface";
import { getDrillholes } from "../redux/threedSlice/thunks";
import { selectDrillholes } from "../redux/threedSlice/selectors";

export const useGetListDrillhole = () => {
  const dispatch = useDispatch();
  const { data, status, error } = useSelector(selectDrillholes);
  const [drillholes, setDrillholes] = useState<DrillholeData[]>([]);

  useEffect(() => {
    if (status === RequestState.idle) {
      dispatch(getDrillholes());
    }

    if (status === RequestState.success && data) {
      setDrillholes(data);
    }
  }, [dispatch, status, data]);

  return {
    drillholes,
    isLoading: status === RequestState.pending,
    error,
  };
};
```

```typescript
// src/modules/threed/hooks/useGetGeologyData.hook.tsx
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RequestState } from "@/common/configs/app.contants";
import { getGeologyData } from "../redux/threedSlice/thunks";
import { selectGeologyData } from "../redux/threedSlice/selectors";

export const useGetGeologyData = (drillholeId: string) => {
  const dispatch = useDispatch();
  const geologyState = useSelector(selectGeologyData(drillholeId)) || {
    data: [],
    status: RequestState.idle,
  };

  useEffect(() => {
    if (geologyState.status === RequestState.idle) {
      dispatch(getGeologyData(drillholeId));
    }
  }, [dispatch, drillholeId, geologyState.status]);

  return {
    data: geologyState.data,
    isLoading: geologyState.status === RequestState.pending,
    error: geologyState.error,
  };
};
```

### Component Implementation

Following the image module pattern, the main components will be organized as follows:

#### Main View Component

```tsx
// src/modules/threed/components/drillhole-view-panel.tsx
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Tabs } from "antd";
import DrillholeView from "./drillhole-view";
import DrillholeTable from "./drillhole-table";
import FilterDrillhole from "./filter-drillhole";
import LegendPanel from "./legend-panel";
import { useGetListDrillhole } from "../hooks/useGetListDrillhole.hook";

const { TabPane } = Tabs;

const DrillholeViewPanel: React.FC = () => {
  const { isLoading, drillholes } = useGetListDrillhole();

  return (
    <div className="flex flex-col h-full">
      <div className="flex mb-4">
        <FilterDrillhole />
      </div>

      <Tabs defaultActiveKey="view" className="flex-1">
        <TabPane tab="3D View" key="view">
          <div className="flex h-full">
            <div className="flex-1">
              <DrillholeView />
            </div>
            <div className="w-64 ml-4">
              <LegendPanel />
            </div>
          </div>
        </TabPane>
        <TabPane tab="Drillhole Data" key="data">
          <DrillholeTable drillholes={drillholes} loading={isLoading} />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DrillholeViewPanel;
```

#### 3D View Component

```tsx
// src/modules/threed/components/drillhole-view.tsx
import React, { useRef, useEffect } from "react";
import { useSelector } from "react-redux";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Environment } from "@react-three/drei";
import DrillholeModel from "./canvas/drillhole-model";
import TopographyModel from "./canvas/topography";
import SectionPlane from "./canvas/section-plane";
import ControlPanel from "./control-panel";
import { useGetListDrillhole } from "../hooks/useGetListDrillhole.hook";
import {
  selectSelectedDrillholes,
  selectShowLabels,
  selectSectionConfig,
} from "../redux/threedSlice/selectors";

const DrillholeView: React.FC = () => {
  const { drillholes, isLoading } = useGetListDrillhole();
  const selectedDrillholes = useSelector(selectSelectedDrillholes);
  const showLabels = useSelector(selectShowLabels);
  const sectionConfig = useSelector(selectSectionConfig);

  return (
    <div className="relative h-full w-full">
      <Canvas camera={{ position: [10, 10, 10], fov: 50 }}>
        <color attach="background" args={["#f0f0f0"]} />
        <ambientLight intensity={0.5} />
        <directionalLight position={[10, 10, 5]} intensity={0.8} />

        {!isLoading && (
          <>
            <TopographyModel />

            {drillholes
              .filter((d) => selectedDrillholes.includes(d.id))
              .map((drillhole) => (
                <DrillholeModel
                  key={drillhole.id}
                  drillhole={drillhole}
                  showLabel={showLabels}
                />
              ))}

            {sectionConfig.enabled && (
              <SectionPlane
                direction={sectionConfig.direction}
                position={sectionConfig.position}
              />
            )}

            <OrbitControls
              enableDamping
              dampingFactor={0.05}
              minDistance={2}
              maxDistance={100}
            />
            <gridHelper args={[100, 100]} />
            <Environment preset="sunset" />
          </>
        )}
      </Canvas>

      <div className="absolute top-4 right-4">
        <ControlPanel />
      </div>
    </div>
  );
};

export default DrillholeView;
```

#### Canvas Components

The individual canvas components will be implemented in the `components/canvas/` directory:

```tsx
// src/modules/threed/components/canvas/drillhole-model.tsx
import React, { useMemo } from "react";
import * as THREE from "three";
import { Text } from "@react-three/drei";
import { DrillholeData } from "../../interface/threed.interface";
import { createDrillholeGeometry } from "../../helpers/drillhole.helper";
import { useGetGeologyData } from "../../hooks/useGetGeologyData.hook";
import GeologyModel from "./geology-model";

interface DrillholeModelProps {
  drillhole: DrillholeData;
  showLabel: boolean;
}

const DrillholeModel: React.FC<DrillholeModelProps> = ({
  drillhole,
  showLabel,
}) => {
  const { data: geologyData, isLoading } = useGetGeologyData(drillhole.id);

  // Create geometry from drillhole data
  const geometry = useMemo(() => {
    // For demonstration, create a simple vertical line representing the drillhole
    // In a real implementation, we would need survey points to create accurate holes
    const points = [
      new THREE.Vector3(drillhole.east, drillhole.north, drillhole.rl),
      new THREE.Vector3(
        drillhole.east,
        drillhole.north,
        drillhole.rl - drillhole.depth
      ),
    ];

    const curve = new THREE.CatmullRomCurve3(points);
    return new THREE.TubeGeometry(
      curve,
      20, // tubularSegments
      2.0, // radius
      8, // radialSegments
      false // closed
    );
  }, [drillhole]);

  return (
    <group>
      <mesh geometry={geometry}>
        <meshStandardMaterial color="#666" />
      </mesh>

      {!isLoading && geologyData && (
        <GeologyModel drillholeId={drillhole.id} geologyData={geologyData} />
      )}

      {showLabel && (
        <Text
          position={[drillhole.east, drillhole.north, drillhole.rl + 5]}
          fontSize={5}
          color="#000"
          anchorX="center"
          anchorY="middle"
        >
          {drillhole.id}
        </Text>
      )}
    </group>
  );
};

export default DrillholeModel;
```

## 4. Technical Details

### Data Flow Architecture

```mermaid
graph TD
    A[Redux Store] -->|Global State| B[DrillholeView Component]
    C[API/Redux Thunks] -->|Data Loading & Processing| A
    D[User Interactions] -->|Dispatch Actions| C
    E[Component State] -->|Local Component State| B
    F[Drillhole Data] -->|Parse & Transform| C
    G[Three.js Scene] -->|Render| B
    H[UI Components] -->|UI Updates| B
    I[Camera Controls] -->|Animation State| E
    J[Section Planes] -->|Clipping State| A
```

### Component Relationships

```mermaid
graph TD
    A[DrillholeViewPanel] -->|Contains| B[DrillholeView]
    A -->|Contains| C[DrillholeTable]
    A -->|Contains| D[FilterDrillhole]
    A -->|Contains| E[LegendPanel]
    B -->|Renders| F[DrillholeModel]
    B -->|Renders| G[TopographyModel]
    B -->|Renders| H[SectionPlane]
    F -->|Contains| I[GeologyModel]
    J[Redux Store] -.->|Data Flow| A
```

### Helper Functions and Hooks

```typescript
// src/modules/threed/helpers/drillhole.helper.ts
import * as THREE from "three";
import { DrillholeData } from "../interface/threed.interface";

export interface DrillholeGeometryConfig {
  tubeRadius: number;
  geologyTubeRadius: number;
  verticalExaggeration: number;
  resolution: number;
}

// Helper to create drillhole geometry from drillhole data (EAST, NORTH, RL, DEPTH)
export const createDrillholeGeometry = (
  drillhole: DrillholeData,
  config: DrillholeGeometryConfig
) => {
  // Create points for the drillhole path (currently just a straight vertical line)
  const points = [
    new THREE.Vector3(drillhole.east, drillhole.north, drillhole.rl),
    new THREE.Vector3(
      drillhole.east,
      drillhole.north,
      drillhole.rl - drillhole.depth * config.verticalExaggeration
    ),
  ];

  // Create a curve from the points
  const curve = new THREE.CatmullRomCurve3(points);

  // Create a tube geometry along the curve
  return new THREE.TubeGeometry(
    curve,
    config.resolution,
    config.tubeRadius,
    8, // radial segments
    false // closed
  );
};

// Future enhancement: Add support for actual survey points
// This would allow for non-straight drillhole visualization
```

### Interfaces for Type Safety

```typescript
// src/modules/threed/interface/threed.interface.ts
// DrillHole data structure based on CSV format:
// HOLEID,PROJECTCODE,EAST,NORTH,RL,DEPTH

export interface DrillholeData {
  id: string; // HOLEID in CSV
  projectCode: string;
  east: number; // X coordinate
  north: number; // Y coordinate
  rl: number; // Z coordinate (Relative Level / elevation)
  depth: number; // Total depth of the hole
}

export interface GeologySegment {
  from: number;
  to: number;
  rockType: string;
  color: string;
}

export interface GeologyData {
  drillholeId: string;
  segments: GeologySegment[];
}

export interface ThreedQuery {
  projectId?: string;
  page?: number;
  pageSize?: number;
}
```

### Camera Controls

```typescript
// src/modules/threed/hooks/useCameraControls.ts
import { useThree } from "@react-three/fiber";
import { useDispatch } from "react-redux";
import { useCallback } from "react";
import { updateCameraPosition } from "../redux/threedSlice/threed.slice";

interface CameraPreset {
  position: [number, number, number];
  target: [number, number, number];
}

interface CameraPresets {
  top: CameraPreset;
  east: CameraPreset;
  north: CameraPreset;
  iso: CameraPreset;
}

export const useCameraControls = () => {
  const { camera, scene } = useThree();
  const dispatch = useDispatch();

  const presets: CameraPresets = {
    top: {
      position: [0, 100, 0],
      target: [0, 0, 0],
    },
    east: {
      position: [100, 0, 0],
      target: [0, 0, 0],
    },
    north: {
      position: [0, 0, 100],
      target: [0, 0, 0],
    },
    iso: {
      position: [50, 50, 50],
      target: [0, 0, 0],
    },
  };

  const setPreset = useCallback(
    (presetName: keyof CameraPresets) => {
      const preset = presets[presetName];
      dispatch(updateCameraPosition(preset));
    },
    [dispatch, presets]
  );

  return { setPreset };
};
```

## 5. Performance Optimizations

For optimal performance, the implementation will include:

1. **Geometry Caching System**

   ```typescript
   // src/modules/threed/hooks/useGeometryCache.ts
   import { useRef, useCallback, useEffect } from "react";
   import * as THREE from "three";

   export const useGeometryCache = () => {
     const cache = useRef(new Map<string, THREE.BufferGeometry>());

     const getGeometry = useCallback(
       (key: string, generator: () => THREE.BufferGeometry) => {
         if (!cache.current.has(key)) {
           cache.current.set(key, generator());
         }
         return cache.current.get(key)!;
       },
       []
     );

     useEffect(() => {
       return () => {
         // Cleanup geometries on unmount
         cache.current.forEach((geometry) => geometry.dispose());
         cache.current.clear();
       };
     }, []);

     return getGeometry;
   };
   ```

2. **Level of Detail Management**

   ```typescript
   // src/modules/threed/hooks/useLOD.ts
   import { useThree } from "@react-three/fiber";
   import { useCallback, useMemo } from "react";
   import * as THREE from "three";

   export const useLOD = (bounds: THREE.Box3) => {
     const { camera } = useThree();

     return useCallback(
       (position: THREE.Vector3) => {
         const distance = camera.position.distanceTo(position);

         if (distance > 300) return { visible: false };

         return {
           visible: true,
           detail: distance < 100 ? "high" : distance < 200 ? "medium" : "low",
           opacity: Math.min(1, Math.max(0.3, 1 - (distance - 100) / 200)),
         };
       },
       [camera]
     );
   };
   ```

3. **Frustum Culling**

   ```typescript
   // src/modules/threed/helpers/culling.helper.ts
   import * as THREE from "three";

   export const isCulled = (
     position: THREE.Vector3,
     frustum: THREE.Frustum,
     bounds: THREE.Box3,
     radius: number = 20
   ) => {
     // Quick sphere-based intersection test
     if (!frustum.intersectsSphere(new THREE.Sphere(position, radius))) {
       return true;
     }

     // Additional box test for more precision
     const box = new THREE.Box3().setFromCenterAndSize(
       position,
       new THREE.Vector3(radius * 2, radius * 2, radius * 2)
     );

     return !frustum.intersectsBox(box);
   };
   ```

4. **Cross-Section System**

   ```typescript
   // src/modules/threed/components/canvas/section-plane.tsx
   import React, { useMemo, useEffect } from "react";
   import * as THREE from "three";

   interface SectionPlaneProps {
     direction: "ew" | "ns";
     position: number; // 0-100%
   }

   const SectionPlane: React.FC<SectionPlaneProps> = ({
     direction,
     position,
   }) => {
     const material = useMemo(() => {
       return new THREE.MeshBasicMaterial({
         color: 0xffff00,
         side: THREE.DoubleSide,
         transparent: true,
         opacity: 0.2,
       });
     }, []);

     const plane = useMemo(() => {
       const normal =
         direction === "ew"
           ? new THREE.Vector3(0, 0, 1) // EW plane (looking north)
           : new THREE.Vector3(1, 0, 0); // NS plane (looking east)

       return new THREE.Plane(normal, 0);
     }, [direction]);

     // Update position based on percentage
     useEffect(() => {
       // Calculate position based on scene bounds
       const posValue = -50 + position; // Convert 0-100% to relative position
       plane.constant = -posValue;
     }, [position, plane]);

     return (
       <group>
         <planeHelper args={[plane, 100, 0xffff00]} />
         <mesh>
           <planeGeometry args={[200, 200]} />
           <primitive object={material} />
         </mesh>
       </group>
     );
   };

   export default SectionPlane;
   ```

## 7. Integration with 3D-Viewing Page

The existing `/src/app/3d-viewing` route will be updated to use our new module structure:

```tsx
// src/app/3d-viewing/page.tsx
"use client";

import DrillholeViewPanel from "@/modules/threed/components/drillhole-view-panel";

export default function ThreeDViewing() {
  return (
    <main className="flex min-h-screen flex-col p-6">
      <h1 className="text-2xl font-bold mb-4">3D Drillhole Visualization</h1>
      <DrillholeViewPanel />
    </main>
  );
}
```

## 8. Conclusion

This implementation plan provides a comprehensive approach to recreating the 3D drilling visualization functionality using React Three Next. The updated component-based architecture follows the established pattern from the image module, making the codebase more maintainable and extensible, while leveraging React Three Next's declarative approach to simplify complex Three.js operations.

Key advantages of this implementation:

1. **Consistent Structure**: Following the established pattern from the image module improves maintainability
2. **Improved Code Organization**: Clear separation of concerns with specialized components and hooks
3. **Better State Management**: Combination of Redux for global state and local state management
4. **Enhanced Performance**: Optimized geometry generation and efficient updates
5. **Maintainable Architecture**: Modular design with reusable components
6. **Type Safety**: Full TypeScript support for better development experience

By following this plan, we can successfully implement the 3D visualization feature with a consistent structure across the application while preserving all the functionality described in the original plan and adding new features for improved user experience.

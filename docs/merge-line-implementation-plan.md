# Merge Line Feature Implementation Plan

## Overview

This document outlines the implementation plan for adding a "Merge Line" feature to the logging module. This feature will allow users to select two lines and merge them into one, provided they belong to the same image crop ID.

## Requirements

1. Add a "Merge Line" button next to the existing "Split Line" button
2. The button should toggle the merge line mode on/off
3. When enabled, users can select two lines to merge them
4. The two lines must belong to the same `imageCropId`
5. When merging, the right line will be deleted and the left line's `endX` will be changed to the right line's `endX`
6. Error handling for invalid merge attempts

## Implementation Details

### 1. Redux Store Changes

#### File: `src/modules/logging/redux/loggingSlice/logging.slice.ts`

```typescript
// Add to initialState
isMergeLine: false,
selectedLine: null,

// Add new reducers
updateIsMergeLine: (state, action: PayloadAction<boolean>) => {
  state.isMergeLine = action.payload;
  // Reset selectedLine when toggling merge mode
  if (!action.payload) {
    state.selectedLine = null;
  }
},
updateSelectedLine: (state, action: PayloadAction<any>) => {
  state.selectedLine = action.payload;
},

// Update AccountSettingsSliceState interface
export interface AccountSettingsSliceState {
  // ... existing properties
  isMergeLine: boolean;
  selectedLine: any; // Stores the first selected line during merge operation
}

// Export the new actions
export const {
  // ... existing exports
  updateIsMergeLine,
  updateSelectedLine,
} = loggingSlice.actions;
```

### 2. UI Changes

#### File: `src/modules/logging/components/logging-stage.tsx`

Add the Merge Line button next to the Split Line button:

```tsx
{
  isShowSegmentation && (
    <Tooltip title={`Merge line`} placement="left">
      <div
        onClick={() => {
          dispatch(updateIsMergeLine(!isMergeLine));
        }}
        className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
          isMergeLine
            ? "bg-primary text-white border-primary border-t border-t-white"
            : "bg-white hover:bg-gray-100"
        }`}
      >
        <MergeOutlined width={20} />
      </div>
    </Tooltip>
  );
}
```

Add the necessary imports:

```tsx
import {
  // ... existing imports
  MergeOutlined,
} from "@ant-design/icons";
import {
  // ... existing imports
  updateIsMergeLine,
} from "../redux/loggingSlice";
```

Add the state selector:

```tsx
const isMergeLine = useAppSelector((state) => state.logging.isMergeLine);
```

### 3. Line Merging Logic

#### File: `src/modules/logging/components/image-row-logging.tsx`

Add the necessary imports and selectors:

```tsx
import {
  // ... existing imports
  updateIsMergeLine,
  updateSelectedLine,
} from "../redux/loggingSlice";

// Add state selectors
const isMergeLine = useAppSelector((state) => state.logging.isMergeLine);
const selectedLine = useAppSelector((state) => state.logging.selectedLine);
```

Update the line click handler to handle merge functionality:

```tsx
// Enhanced line click handler to implement merging logic
const handleLineClick = (
  e: KonvaEventObject<MouseEvent>,
  segmentationName: string,
  lineId: number,
  line: any
) => {
  e.cancelBubble = true; // Prevent event propagation to parent components

  // If in split line mode, use existing functionality
  if (isSplitLine) {
    // Existing split line code...
    return;
  }

  // If in merge line mode
  if (isMergeLine) {
    if (!selectedLine) {
      // First line selection
      dispatch(updateSelectedLine({ id: lineId, line, segmentationName }));
      toast.info("First line selected. Click another line to merge.");
    } else {
      // Second line selection - perform merge
      if (selectedLine.id === lineId) {
        toast.info(
          "You selected the same line. Please select a different line to merge."
        );
        return;
      }

      // Check if both lines belong to the same imageCropId
      if (line.imageCropId !== selectedLine.line.imageCropId) {
        toast.error("Cannot merge lines from different images");
        dispatch(updateSelectedLine(null));
        return;
      }

      // Determine left and right lines
      const isSelectedLineLeft =
        Math.min(selectedLine.line.startX, selectedLine.line.endX) <
        Math.min(line.startX, line.endX);

      const leftLine = isSelectedLineLeft ? selectedLine.line : line;
      const rightLine = isSelectedLineLeft ? line : selectedLine.line;
      const leftLineId = isSelectedLineLeft ? selectedLine.id : lineId;
      const rightLineId = isSelectedLineLeft ? lineId : selectedLine.id;

      // Perform merge operation
      mergeLinesAPI(leftLineId, rightLineId, leftLine, rightLine);
    }
  }
};

// Add merge lines API function
const mergeLinesAPI = async (
  leftLineId: number,
  rightLineId: number,
  leftLine: any,
  rightLine: any
) => {
  try {
    // Update the left line to extend to the end of the right line
    const updateParams = {
      id: leftLineId,
      endX: rightLine.endX,
    };

    // Call API to update the left line
    const updateResult = await recoveryRequest.updateRockLine(updateParams);

    if (updateResult.state === RequestState.error) {
      toast.error(updateResult.message || "Failed to merge lines");
      dispatch(updateSelectedLine(null));
      return;
    }

    // Delete the right line
    const deleteResult = await recoveryRequest.deleteRockLine(rightLineId);

    if (deleteResult.state === RequestState.error) {
      toast.error(
        deleteResult.message || "Failed to delete second line after merge"
      );
      dispatch(updateSelectedLine(null));
      return;
    }

    // Success! Show feedback
    toast.success("Lines merged successfully");

    // Reset the selected line
    dispatch(updateSelectedLine(null));

    // Update the visual representation
    setSegmentationLines((prev) => {
      // Filter out the right line and update the left line
      const updatedLines = prev
        .filter((line) => line.id !== rightLineId)
        .map((line) => {
          if (line.id === leftLineId) {
            return {
              ...line,
              endX: rightLine.endX,
            };
          }
          return line;
        });

      return updatedLines;
    });
  } catch (error) {
    toast.error("Error merging lines");
    console.error("Error merging lines:", error);
    dispatch(updateSelectedLine(null));
  }
};
```

### 4. Visual Feedback

Update the line rendering to highlight selected lines:

```tsx
// In the Line component rendering
<Line
  points={
    [
      // ... existing points calculation
    ]
  }
  stroke={
    isSplittingLine && splittingLineId === `line-${line.id}`
      ? "#FFD700" // Gold color for splitting state
      : selectedLine && selectedLine.id === line.id && isMergeLine
      ? "#FF1493" // Pink for selected line in merge mode
      : hoveredLineId === `line-${line.id}` && (isSplitLine || isMergeLine)
      ? "#ff4500" // Orange-red for hover in split or merge mode
      : "#87CEFA" // Default light blue
  }
  strokeWidth={
    (isSplittingLine && splittingLineId === `line-${line.id}`) ||
    (hoveredLineId === `line-${line.id}` && (isSplitLine || isMergeLine)) ||
    (selectedLine && selectedLine.id === line.id && isMergeLine)
      ? 24 // Wider when hovering, splitting, or selected for merging
      : 16 // Default width
  }
  // ... rest of the props
/>
```

### 5. API Methods

Make sure the recoveryRequest service has a deleteRockLine method. If not, it needs to be added to the API service:

#### File: `src/modules/logging/api/recovery.api.ts`

```typescript
const deleteRockLine = async (id: number) => {
  try {
    const response = await httpClient.delete(`/rockline/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting rock line:", error);
    return {
      state: RequestState.error,
      message: "Failed to delete rock line",
    };
  }
};

export default {
  // ... existing methods
  deleteRockLine,
};
```

## Testing Plan

1. **UI Testing**:

   - Verify the Merge Line button appears correctly
   - Check that it toggles properly (visual state changes)

2. **Functionality Testing**:

   - Test selecting one line shows the expected feedback
   - Test selecting a second line from the same image merges correctly
   - Test selecting a line from a different image shows the error message
   - Verify the visual appearance of the merged line

3. **Edge Cases**:
   - Test selecting the same line twice
   - Test toggling mode with a selected line
   - Test page reload behavior

## Implementation Sequence

1. Add Redux state and actions
2. Update the UI components to add the Merge Line button
3. Implement the merge line logic
4. Add visual feedback for selected lines
5. Ensure API methods are available
6. Test all functionality

## Notes

- The implementation follows a pattern similar to the existing Split Line feature
- We're reusing existing API services where possible
- Visual feedback is important for user experience
- Error handling is robust to prevent invalid operations

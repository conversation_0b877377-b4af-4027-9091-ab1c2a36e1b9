import { Input } from "antd";
import * as React from "react";
import { FormItem } from "react-hook-form-antd";

export interface IInputCheckCommonProps {
  label: string;
  name: string;
  control?: any;
}

export function InputCheckCommon(props: IInputCheckCommonProps) {
  const { label, name, control } = props;
  return (
    <FormItem
      control={control}
      name={name}
      label={label}
      className="flex items-center"
    >
      <Input
        className="checkbox checkbox-primary"
        name={name}
        size="large"
        type="checkbox"
      />
    </FormItem>
  );
}

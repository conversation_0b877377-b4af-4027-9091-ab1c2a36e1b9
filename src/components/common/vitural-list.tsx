import React from "react";
import { AutoSizer, List, ListRowRenderer } from "react-virtualized";

interface VituralizeImageListProps {
  height: number;
  rowHeight: number;
  fileList: any[];
  rowRenderer: ListRowRenderer;
}

const AutoSizerTmp: any = AutoSizer;
const ListTmp: any = List;

function VituralizeList({
  height,
  rowHeight,
  fileList,
  rowRenderer,
}: VituralizeImageListProps) {
  return (
    <AutoSizerTmp disableHeight>
      {({ width }) => (
        <ListTmp
          width={width} // Đặt chiều rộng phù hợp với giao diện của bạn
          height={height} // Đặt chiều cao phù hợp với giao diện của bạn
          rowCount={fileList.length}
          rowHeight={rowHeight} // Chiều cao của mỗi item, điều chỉnh theo nhu cầu của bạn
          rowRenderer={rowRenderer}
        />
      )}
    </AutoSizerTmp>
  );
}

export default React.memo(VituralizeList);

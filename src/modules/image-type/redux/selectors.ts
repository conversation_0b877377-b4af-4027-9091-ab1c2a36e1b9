import { ReduxState } from "@/common/vendors/redux/store/store";

export const selectImageType = (state: ReduxState) => state.imageType;
export const selectListImageType = (state: ReduxState) =>
  state.imageType.result;
export const selectGetListImageTypeStatus = (state: ReduxState) =>
  state.imageType.status;

export const selectImageSubType = (state: ReduxState) => state.imageSubType;
export const selectListImageSubType = (state: ReduxState) =>
  state.imageSubType.result;
export const selectGetListImageSubTypeStatus = (state: ReduxState) =>
  state.imageSubType.status;

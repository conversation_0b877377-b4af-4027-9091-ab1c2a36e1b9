import { RequestState } from "@/common/configs/app.contants";
import { createSlice } from "@reduxjs/toolkit";
import { getListImageSubType } from "./thunks";

const initialState: ImageSubTypeSliceState = {
  status: RequestState.idle,
};

export const imageSubTypeSlice = createSlice({
  name: "imageSubType",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getListImageSubType.pending, (state) => {
        state.status = RequestState.pending;
      })
      .addCase(getListImageSubType.fulfilled, (state, action) => {
        state.status = RequestState.success;
        state.result = action.payload.data;
      });
  },
});

export interface ImageSubTypeSliceState {
  result?: any;
  status: RequestState;
}

export const {} = imageSubTypeSlice.actions;

import { ModalType } from "@/common/configs/app.enum";
import { But<PERSON><PERSON>ommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListNumberFields } from "@/modules/list/hooks/number-fields/useGetListNumberFields";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateImageType } from "../hooks/useCreateImageType";
import { useDeleteImageType } from "../hooks/useDeleteImageType";
import { useUpdateImageType } from "../hooks/useUpdateImageType";
import {
  ImageTypeBody,
  ImageTypeBodyType,
} from "../model/schema/image-type.schema";

export interface IModalImageTypeProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalImageType(props: IModalImageTypeProps) {
  const { modalState, setModalState, refresh } = props;
  const { request: requestCreateImageType, loading: loadingCreateImageType } =
    useCreateImageType();
  const { request: requestUpdateImageType, loading: loadingUpdateImageType } =
    useUpdateImageType();
  const { request: requestDeleteImageType, loading: loadingDeleteImageType } =
    useDeleteImageType();
  const { control, handleSubmit } = useForm<ImageTypeBodyType>({
    resolver: zodResolver(ImageTypeBody),
    defaultValues: {
      ...modalState?.detailInfo,
      numberId: modalState?.detailInfo?.number?.id,
      rockTypeId: modalState?.detailInfo?.rockType?.id,
      isActive: modalState?.detailInfo?.isActive ?? true,
    },
  });
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const onSubmit = (values: ImageTypeBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateImageType(
        values,
        (res) => {
          toast.success("Create a image type successfully");
          setModalState({ ...modalState, isOpen: false });
          refresh();
        },
        (err) => {
          toast.error(err?.message);
        }
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateImageType(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update image type successfully");
          setModalState({ ...modalState, isOpen: false });
          refresh();
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteImageType(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        console.log(err);
        toast.error(err?.message);
      }
    );
  };
  const { data: numberFields, request: requestNumberFields } =
    useGetListNumberFields();
  const [keywordNumberField, setKeywordNumberField] = useState("");
  useEffect(() => {
    requestNumberFields({
      maxResultCount: 1000,
      keyword: keywordNumberField,
    });
  }, [keywordNumberField]);

  const { data: rockTypes, request: requestRockTypes } = useGetListRockType();
  const [keywordRockType, setKeywordRockType] = useState("");
  useEffect(() => {
    requestRockTypes({
      maxResultCount: 1000,
      keyword: keywordRockType,
      isActive: true,
    });
  }, [keywordRockType]);
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={500}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this image type?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the image
            type
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteImageType}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update image type"
              : "Add image type"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type image type name here"
              control={control}
            />

            <div className="flex gap-2 flex-wrap">
              <ToogleCommon
                label="Standard"
                name="isStandard"
                control={control}
              />
              <ToogleCommon
                label="Rig Corrected"
                name="isRigCorrected"
                control={control}
              />
              <ToogleCommon label="Active" name="isActive" control={control} />
            </div>

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingCreateImageType || loadingUpdateImageType}
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update image type"
                  : "Add image type"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}

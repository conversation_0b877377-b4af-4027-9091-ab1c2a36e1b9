"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import assayRequest from "@/modules/assay/api/assay.api";
import { useQueryAssaySuite } from "@/modules/assay/hooks/useQueryAssaySuite";
import {
  TypeOfSuite,
  TypeOfSuiteOptions,
} from "@/modules/delete-data/components/delete-data.enum";
import suiteRequest from "@/modules/downhole-point/api/suite.api";
import { useQuerySuite } from "@/modules/downhole-point/hooks/useQuerySuite";
import { useQueryDrillhole } from "@/modules/drillhole/hooks/useQueryDrillhole";
import geologySuiteRequest from "@/modules/geology-suite/api/geology-suite.api";
import { useQueryGeologySuite } from "@/modules/geology-suite/hooks/useQueryGeologySuite";
import geotechSuiteRequest from "@/modules/geotech-suite/api/geotech-suite.api";
import { useQueryGeotechSuite } from "@/modules/geotech-suite/hooks/useQueryGeotechSuite";
import { Button, message, Modal, Select, Spin, Switch } from "antd";
import { useEffect, useState } from "react";
import deleteDataRequest from "../api/delete-data.api";

interface DeleteDataState {
  selectedDrillhole: any;
  selectedSuiteType: TypeOfSuite | null;
  selectedSuite: any;
  deletionType: "suite" | "field";
  selectedField: any;
  showConfirmationModal: boolean;
}

export default function DeleteData() {
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user?.userInfo?.prospectId
  );

  // Query hooks
  const {
    setEnable: setEnableDrillhole,
    data: drillholeData,
    handleScroll: handleScrollDrillhole,
    searchParams: searchParamsDrillhole,
    setSearchParams: setSearchParamsDrillhole,
    isLoading: isLoadingDrillhole,
    enable: enableDrillhole,
    refetch: refetchDrillhole,
  } = useQueryDrillhole();

  const {
    data: geologySuiteData,
    setEnable: setEnableGeologySuite,
    isLoading: isLoadingGeologySuite,
    handleScroll: handleScrollGeologySuite,
    searchParams: searchParamsGeologySuite,
    setSearchParams: setSearchParamsGeologySuite,
  } = useQueryGeologySuite();

  const {
    data: assaySuiteData,
    setEnable: setEnableAssaySuite,
    isLoading: isLoadingAssaySuite,
    handleScroll: handleScrollAssaySuite,
    searchParams: searchParamsAssaySuite,
    setSearchParams: setSearchParamsAssaySuite,
  } = useQueryAssaySuite();

  const {
    data: geophysicsSuiteData,
    setEnable: setEnableGeophysicsSuite,
    isLoading: isLoadingGeophysicsSuite,
    handleScroll: handleScrollGeophysicsSuite,
    searchParams: searchParamsGeophysicsSuite,
    setSearchParams: setSearchParamsGeophysicsSuite,
  } = useQuerySuite();

  const {
    data: geotechSuiteData,
    setEnable: setEnableGeotechSuite,
    isLoading: isLoadingGeotechSuite,
    handleScroll: handleScrollGeotechSuite,
    searchParams: searchParamsGeotechSuite,
    setSearchParams: setSearchParamsGeotechSuite,
  } = useQueryGeotechSuite();

  // Local state
  const [state, setState] = useState<DeleteDataState>({
    selectedDrillhole: null,
    selectedSuiteType: null,
    selectedSuite: null,
    deletionType: "suite",
    selectedField: null,
    showConfirmationModal: false,
  });

  const [suiteFields, setSuiteFields] = useState<any[]>([]);
  const [loadingFields, setLoadingFields] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (globalProjectId && globalProspectId) {
      setEnableDrillhole(true);
      setSearchParamsDrillhole((prev) => {
        const newParams = {
          ...prev,
          projectIds: [globalProjectId],
          prospectIds: [globalProspectId],
        };
        return newParams;
      });
    }
  }, [globalProjectId, globalProspectId]);

  // Reset suite and field when suite type changes
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      selectedSuite: null,
      selectedField: null,
    }));
    setSuiteFields([]);
  }, [state.selectedSuiteType]);

  // Reset field when suite changes
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      selectedField: null,
    }));
    setSuiteFields([]);
  }, [state.selectedSuite]);

  // Fetch suite fields when suite is selected and deletion type is 'field'
  useEffect(() => {
    const fetchSuiteFields = async () => {
      if (!state.selectedSuite || state.deletionType !== "field") {
        setSuiteFields([]);
        return;
      }

      setLoadingFields(true);
      try {
        let response;

        switch (state.selectedSuiteType) {
          case TypeOfSuite.Geology:
            response = await geologySuiteRequest.getDetail(
              state.selectedSuite.toString()
            );
            if (response.state === "success" && response.data) {
              setSuiteFields(response.data.geologySuiteFields || []);
            }
            break;
          case TypeOfSuite.Assay:
            response = await assayRequest.getAssaySuite({
              Id: state.selectedSuite,
            });
            if (response.state === "success" && response.data) {
              setSuiteFields(response.data.assayAttributes || []);
            }
            break;
          case TypeOfSuite.Geophysics:
            response = await suiteRequest.getAttributes({
              id: state.selectedSuite,
            });
            if (response.state === "success" && response.data) {
              setSuiteFields(response.data || []);
            }
            break;
          case TypeOfSuite.Geotech:
            response = await geotechSuiteRequest.getDetail(
              state.selectedSuite.toString()
            );
            if (response.state === "success" && response.data) {
              setSuiteFields(response.data.structures || []);
            }
            break;
        }
      } catch (error) {
        console.error("Error fetching suite fields:", error);
        message.error("Failed to load suite fields");
      } finally {
        setLoadingFields(false);
      }
    };

    fetchSuiteFields();
  }, [state.selectedSuite, state.selectedSuiteType, state.deletionType]);

  // Get suites based on selected type
  const getSuitesForType = () => {
    switch (state.selectedSuiteType) {
      case TypeOfSuite.Geology:
        return geologySuiteData?.data?.items || [];
      case TypeOfSuite.Assay:
        return assaySuiteData?.data?.items || [];
      case TypeOfSuite.Geophysics:
        return geophysicsSuiteData?.data?.result?.items || [];
      case TypeOfSuite.Geotech:
        return geotechSuiteData?.data?.items || [];
      default:
        return [];
    }
  };

  const getPropForType = () => {
    switch (state.selectedSuiteType) {
      case TypeOfSuite.Geology:
        return {
          options: geologySuiteData?.data?.items || [],
          searchParams: searchParamsGeologySuite,
          setSearchParams: setSearchParamsGeologySuite,
          handleScroll: handleScrollGeologySuite,
        };
      case TypeOfSuite.Assay:
        return {
          options: assaySuiteData?.data?.items || [],
          searchParams: searchParamsAssaySuite,
          setSearchParams: setSearchParamsAssaySuite,
          handleScroll: handleScrollAssaySuite,
        };
      case TypeOfSuite.Geophysics:
        return {
          options: geophysicsSuiteData?.data?.result?.items || [],
          searchParams: searchParamsGeophysicsSuite,
          setSearchParams: setSearchParamsGeophysicsSuite,
          handleScroll: handleScrollGeophysicsSuite,
        };
      case TypeOfSuite.Geotech:
        return {
          options: geotechSuiteData?.data?.items || [],
          searchParams: searchParamsGeotechSuite,
          setSearchParams: setSearchParamsGeotechSuite,
          handleScroll: handleScrollGeotechSuite,
        };
      default:
        return {
          options: [],
          searchParams: {},
          setSearchParams: () => {},
          handleScroll: () => {},
        };
    }
  };
  // Get loading state for suites
  const getSuitesLoading = () => {
    switch (state.selectedSuiteType) {
      case TypeOfSuite.Geology:
        return isLoadingGeologySuite;
      case TypeOfSuite.Assay:
        return isLoadingAssaySuite;
      case TypeOfSuite.Geophysics:
        return isLoadingGeophysicsSuite;
      case TypeOfSuite.Geotech:
        return isLoadingGeotechSuite;
      default:
        return false;
    }
  };

  // Handle proceed with deletion
  const handleProceedWithDeletion = () => {
    if (!state.selectedDrillhole) {
      message.error("Please select a drill hole");
      return;
    }
    if (!state.selectedSuiteType) {
      message.error("Please select a suite type");
      return;
    }
    if (!state.selectedSuite) {
      message.error("Please select a suite");
      return;
    }
    if (state.deletionType === "field" && !state.selectedField) {
      message.error("Please select a field");
      return;
    }

    setState((prev) => ({ ...prev, showConfirmationModal: true }));
  };

  // Handle actual deletion
  const handleDeleteData = async () => {
    //delete data
    const res = await deleteDataRequest.deleteData({
      drillholeId: state.selectedDrillhole,
      suiteType: state.selectedSuiteType,
      suiteId: state.selectedSuite,
      fieldId: state.selectedField,
    });
    if (res.state === RequestState.success) {
      message.success("Data deleted successfully");
      setState((prev) => ({ ...prev, showConfirmationModal: false }));
    } else {
      message.error(res.message || "Failed to delete data");
    }
  };
  useEffect(() => {
    if (state.selectedSuiteType === TypeOfSuite.Geology) {
      setEnableGeologySuite(true);
    }
    if (state.selectedSuiteType === TypeOfSuite.Assay) {
      setEnableAssaySuite(true);
    }
    if (state.selectedSuiteType === TypeOfSuite.Geophysics) {
      setEnableGeophysicsSuite(true);
    }
    if (state.selectedSuiteType === TypeOfSuite.Geotech) {
      setEnableGeotechSuite(true);
    }
  }, [state.selectedSuiteType]);
  return (
    <div className="flex flex-col gap-6 p-6 ">
      <h2 className="text-2xl font-bold text-gray-800">
        Drill Hole Data Deletion
      </h2>

      {/* Drill Hole Selector */}
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium text-gray-700">
          Select Drill Hole
        </label>
        <Select
          size="large"
          placeholder="Select an active drill hole"
          options={drillholeData?.data?.items?.map((item: any) => ({
            label: item.name,
            value: item.id,
          }))}
          value={state.selectedDrillhole}
          onChange={(value) =>
            setState((prev) => ({ ...prev, selectedDrillhole: value }))
          }
          showSearch
          onSearch={(value) => {
            setSearchParamsDrillhole({
              ...searchParamsDrillhole,
              keyword: value,
            });
          }}
          onPopupScroll={(e) => {
            handleScrollDrillhole(e as any);
          }}
          filterOption={false}
          searchValue={searchParamsDrillhole?.keyword}
          loading={isLoadingDrillhole}
          allowClear
          disabled={!globalProjectId || !globalProspectId}
        />
      </div>
      <>
        {/* Suite Type Selector */}
        <div className="flex flex-col gap-2">
          <label className="text-sm font-medium text-gray-700">
            Select Suite Type
          </label>
          <Select
            size="large"
            placeholder="Select suite type"
            options={TypeOfSuiteOptions}
            value={state.selectedSuiteType}
            onChange={(value) =>
              setState((prev) => ({ ...prev, selectedSuiteType: value }))
            }
            allowClear
          />
        </div>
        <>
          {/* Suite Selector */}
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium text-gray-700">
              Select Suite for Data Deletion
            </label>
            <Select
              size="large"
              placeholder="Select suite"
              options={getPropForType().options.map((suite: any) => ({
                label: suite.name,
                value: suite.id,
              }))}
              onSearch={(value) => {
                getPropForType().setSearchParams((prev) => ({
                  ...prev,
                  keyword: value,
                }));
              }}
              onPopupScroll={(e) => {
                getPropForType().handleScroll(e as any);
              }}
              searchValue={getPropForType().searchParams.keyword}
              filterOption={false}
              value={state.selectedSuite}
              onChange={(value) =>
                setState((prev) => ({ ...prev, selectedSuite: value }))
              }
              loading={getSuitesLoading()}
              allowClear
              showSearch
            />
          </div>

          {state.selectedSuite && (
            <>
              {/* Deletion Type Switch */}
              <div className="flex flex-col gap-2">
                <label className="text-sm font-medium text-gray-700">
                  Deletion Type
                </label>
                <div className="flex items-center gap-4">
                  <Switch
                    checked={state.deletionType === "field"}
                    onChange={(checked) =>
                      setState((prev) => ({
                        ...prev,
                        deletionType: checked ? "field" : "suite",
                      }))
                    }
                  />
                  <span className="text-sm text-gray-600">
                    {state.deletionType === "suite"
                      ? "Delete Suite Records"
                      : "Delete Field Data"}
                  </span>
                </div>
              </div>

              {/* Field Selector (only show when deletion type is 'field') */}
              {state.deletionType === "field" && (
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-medium text-gray-700">
                    Select Field for Data Deletion
                  </label>
                  <Select
                    size="large"
                    placeholder="Select field"
                    options={suiteFields.map((field: any) => ({
                      label:
                        field.name ||
                        field.geologyField?.name ||
                        field.attribute?.name,
                      value: field.id,
                    }))}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    value={state.selectedField}
                    onChange={(value) =>
                      setState((prev) => ({
                        ...prev,
                        selectedField: value,
                      }))
                    }
                    loading={loadingFields}
                    allowClear
                    notFoundContent={
                      loadingFields ? (
                        <div className="text-center py-2">
                          <Spin size="small" />
                        </div>
                      ) : (
                        <div className="text-center py-2 text-gray-500">
                          No fields available
                        </div>
                      )
                    }
                  />
                </div>
              )}

              {/* Proceed Button */}
              <Button
                type="primary"
                size="large"
                onClick={handleProceedWithDeletion}
                className="mt-4"
                disabled={
                  !state.selectedDrillhole ||
                  !state.selectedSuiteType ||
                  !state.selectedSuite ||
                  (state.deletionType === "field" && !state.selectedField)
                }
              >
                Proceed with Data Deletion
              </Button>
            </>
          )}
        </>
      </>

      {/* Confirmation Modal */}
      <Modal
        title="Confirm Data Deletion"
        open={state.showConfirmationModal}
        onCancel={() =>
          setState((prev) => ({ ...prev, showConfirmationModal: false }))
        }
        footer={[
          <Button
            key="cancel"
            onClick={() =>
              setState((prev) => ({ ...prev, showConfirmationModal: false }))
            }
          >
            Cancel
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            onClick={handleDeleteData}
            loading={deleting}
          >
            Delete the Data
          </Button>,
        ]}
        width={500}
      >
        <div className="py-4">
          <p className="text-red-600 font-medium mb-4">
            Data deletion cannot be undone. Do not proceed unless you know what
            you are doing. Consider exporting data for the selected drill hole
            before you delete data.
          </p>
          <div className="bg-gray-50 p-4 rounded">
            <p className="text-sm text-gray-700">
              <strong>Drill Hole:</strong>{" "}
              {
                drillholeData?.data?.items?.find(
                  (dh: any) => dh.id === state.selectedDrillhole
                )?.name
              }
            </p>
            <p className="text-sm text-gray-700">
              <strong>Suite Type:</strong>{" "}
              {
                TypeOfSuiteOptions.find(
                  (opt) => opt.value === state.selectedSuiteType
                )?.label
              }
            </p>
            <p className="text-sm text-gray-700">
              <strong>Suite:</strong>{" "}
              {
                getSuitesForType().find(
                  (suite: any) => suite.id === state.selectedSuite
                )?.name
              }
            </p>
            <p className="text-sm text-gray-700">
              <strong>Deletion Type:</strong>{" "}
              {state.deletionType === "suite"
                ? "Delete Suite Records"
                : "Delete Field Data"}
            </p>
            {state.deletionType === "field" && state.selectedField && (
              <p className="text-sm text-gray-700">
                <strong>Field:</strong>{" "}
                {suiteFields.find(
                  (field: any) => field.id === state.selectedField
                )?.name ||
                  suiteFields.find(
                    (field: any) => field.id === state.selectedField
                  )?.geologyField?.name ||
                  suiteFields.find(
                    (field: any) => field.id === state.selectedField
                  )?.attribute?.name}
              </p>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
}

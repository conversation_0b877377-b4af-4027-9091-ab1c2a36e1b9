import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";
import { DeleteDataBodyType } from "../model/schema/delete-data.schema";

const deleteDataRequest = {
  // Main delete function that routes to appropriate deletion method
  deleteData: async (params: DeleteDataBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/DataEntry/DeleteDataTool`,
        params
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default deleteDataRequest;

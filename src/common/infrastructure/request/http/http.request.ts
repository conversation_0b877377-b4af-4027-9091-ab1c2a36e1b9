import { appContants } from "@/common/configs";
import { appConfigs } from "@/common/configs/app.configs";
import IRequest from "@/common/interfaces/request/IRequest";
import { IRequestOptions } from "@/common/interfaces/request/IRequestOptions";
import { AxiosSymbol, ShareSymbol } from "@/common/interfaces/share.types";
import type IStorage from "@/common/interfaces/storage/IStorage";
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import { inject, injectable } from "inversify";
import Cookies from "js-cookie";
import queryString from "query-string";
import { toast } from "react-toastify";
@injectable()
export class HttpRequest implements IRequest {
  private _httpClient: AxiosInstance;
  private _appStorage: IStorage;
  private isRefreshing = false;
  private failedQueue: any[] = [];

  private processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach((prom) => {
      if (error) {
        prom.reject(error);
      } else {
        prom.resolve(token);
      }
    });
    this.failedQueue = [];
  }

  private async handleTokenRefresh() {
    try {
      const refreshToken = this._appStorage.getItem(appContants.refreshToken);
      if (!refreshToken) {
        throw new Error("No refresh token available");
      }

      const response = await this._httpClient.post(
        appConfigs.env.primaryEndPoint + "/TokenAuth/RefreshToken",
        { refreshToken }
      );

      // Store new tokens
      this._appStorage.setItem(
        appContants.tokenKey,
        response.data.result.accessToken
      );
      this._appStorage.setItem(
        appContants.refreshToken,
        response.data.result.refreshToken
      );

      return response.data.result.accessToken;
    } catch (error) {
      return null;
    }
  }

  public constructor(
    @inject(ShareSymbol.IStorage) appStorage: IStorage,
    @inject(AxiosSymbol.Axios) httpClient: AxiosInstance
  ) {
    httpClient.interceptors.request.use(
      function (config: InternalAxiosRequestConfig) {
        return config;
      },
      function (error) {
        return Promise.reject(error);
      }
    );

    httpClient.interceptors.response.use(
      function (response: AxiosResponse) {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // TODO: Current disable refresh token
        // if (
        //   error?.response?.status === 401 &&
        //   !originalRequest._retry
        // ) {
        //   if (this.isRefreshing) {
        //     return new Promise((resolve, reject) => {
        //       this.failedQueue.push({ resolve, reject });
        //     })
        //       .then((token) => {
        //         originalRequest.headers["Authorization"] = "Bearer " + token;
        //         return this._httpClient(originalRequest);
        //       })
        //       .catch((err) => Promise.reject(err));
        //   }

        //   originalRequest._retry = true;
        //   this.isRefreshing = true;

        //   try {
        //     const newAccessToken = await this.handleTokenRefresh();
        //     if (!newAccessToken) {
        //       this.processQueue(new Error("Failed to refresh token"));
        //       this._appStorage.removeItem(appContants.tokenKey);
        //       this._appStorage.removeItem(appContants.refreshToken);
        //       toast.warn("Your session has expired. Please login again.");
        //       window.location.href = "/login";
        //       return Promise.reject(error);
        //     }

        //     this.processQueue(null, newAccessToken);
        //     originalRequest.headers["Authorization"] =
        //       "Bearer " + newAccessToken;
        //     return this._httpClient(originalRequest);
        //   } catch (refreshError) {
        //     this.processQueue(refreshError);
        //     return Promise.reject(refreshError);
        //   } finally {
        //     this.isRefreshing = false;
        //   }
        // }

        if (error.response && error.response.data) {
          return Promise.reject(error.response.data);
        }
        return Promise.reject(error);
      }
    );

    this._httpClient = httpClient;
    this._appStorage = appStorage;
  }

  getOptions(options: IRequestOptions): AxiosRequestConfig {
    const token = this._appStorage.getItem(appContants.tokenKey);
    const opts: AxiosRequestConfig = {
      headers: {
        "Content-Type": "application/json",
        Authorization: token ? "Bearer " + token : "",
      },
      ...options,
    };
    return opts;
  }

  async get<T>(
    path: string,
    params: object | undefined,
    options: IRequestOptions = {}
  ) {
    const _params = params ? queryString.stringify(params) : "";
    const _path = options?.isFullPath
      ? path
      : appConfigs.env.primaryEndPoint + path;
    const _url =
      _path + (_params ? (_path.includes("?") ? "&" : "?") + _params : "");
    const _options = this.getOptions(options);

    const response: AxiosResponse<T> = await this._httpClient.get(
      _url,
      _options
    );
    return response.data;
  }

  async post<T>(path: string, body: object, options: IRequestOptions) {
    const _url = options?.isFullPath
      ? path
      : appConfigs.env.primaryEndPoint + path;
    const _options = this.getOptions(options);
    const response: AxiosResponse<T> = await this._httpClient.post(
      _url,
      body,
      _options
    );

    return response.data;
  }

  async put<T>(path: string, params: object, options: IRequestOptions) {
    const _url = options?.isFullPath
      ? path
      : appConfigs.env.primaryEndPoint + path;
    const _options = this.getOptions(options);
    const response: AxiosResponse<T> = await this._httpClient.put(
      _url,
      params,
      _options
    );
    return response.data;
  }

  async patch<T>(path: string, params: object, options: IRequestOptions) {
    const _url = options?.isFullPath
      ? path
      : appConfigs.env.primaryEndPoint + path;
    const _options = this.getOptions(options);
    const response: AxiosResponse<T> = await this._httpClient.patch(
      _url,
      params,
      _options
    );
    return response.data;
  }

  async delete<T>(path: string, params: object, options: IRequestOptions) {
    const _url = options?.isFullPath
      ? path
      : appConfigs.env.primaryEndPoint + path;
    const _options = this.getOptions(options);

    // delete with params;
    if (params) {
      _options.data = params;
    }

    const response: AxiosResponse<T> = await this._httpClient.delete(
      _url,
      _options
    );
    return response.data;
  }

  async upload<T>(
    path: string,
    appendFields: { key: string; value: any }[],
    options: IRequestOptions,
    onProgress: () => void
  ) {
    const _url = options?.isFullPath
      ? path
      : appConfigs.env.primaryEndPoint + path;

    const _form = new FormData();
    appendFields.forEach((field) => {
      if (field?.value !== null && field?.value !== undefined) {
        _form.append(field.key, field.value);
      }
    });
    const _options = this.getOptions(options);
    if (!_options.headers) _options.headers = {};
    _options.headers["Content-Type"] = "multipart/form-data";
    _options.onUploadProgress = onProgress;

    const response: AxiosResponse<T> = await this._httpClient.post(
      _url,
      _form,
      _options
    );
    return response.data;
  }
}

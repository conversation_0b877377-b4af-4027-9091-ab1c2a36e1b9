import { Modal, InputN<PERSON>ber, Button } from "antd";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { useState } from "react";
import { updateFontSize } from "@/modules/auth/redux/userSlice";
export function ModalChangeFontSize({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const fontSize = useAppSelector((state) => state.user.fontSize);
  const [fontSizeValue, setFontSizeValue] = useState<number>(fontSize ?? 14);
  const dispatch = useAppDispatch();
  return (
    <Modal open={open} onCancel={() => setOpen(false)} footer={null}>
      <div className="flex flex-col gap-4">
        <p className="text-lg font-bold">Font Size</p>
        <InputNumber
          value={fontSizeValue}
          onChange={(value) => setFontSizeValue(value ?? 0)}
        />
        <Button
          type="primary"
          onClick={() => {
            dispatch(updateFontSize(fontSizeValue));
            setOpen(false);
          }}
        >
          Save
        </Button>
      </div>
    </Modal>
  );
}

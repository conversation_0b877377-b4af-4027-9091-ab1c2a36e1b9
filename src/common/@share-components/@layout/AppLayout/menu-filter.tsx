"use client";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { UserRoles } from "@/constants/constant";
import {
  updateProjectId,
  updateProspectId,
} from "@/modules/auth/redux/userSlice";
import { updateSelectedDrilhole } from "@/modules/logging/redux/loggingSlice";
import { useQueryProject } from "@/modules/projects/hooks/useQueryProject";
import { useQueryProspect } from "@/modules/prospect/hooks/useQueryProspect";
import { Select, Spin } from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

interface MenuFilterProps {
  fillSearchParams?: boolean;
}

export default function MenuFilter({ fillSearchParams }: MenuFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });
  const dispatch = useAppDispatch();
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );

  const {
    data: projects,
    setEnable: setEnableProject,
    isLoading: isLoadingProject,
    handleScroll: handleScrollProject,
    searchParams: searchParamsProject,
    setSearchParams: setSearchParamsProject,
  } = useQueryProject();
  useEffect(() => {
    setEnableProject(true);
  }, []);

  //prospect
  const {
    data: prospects,
    setEnable: setEnableProspect,
    enable: enableProspect,
    isLoading: isLoadingProspect,
    handleScroll: handleScrollProspect,
    searchParams: searchParamsProspect,
    setSearchParams: setSearchParamsProspect,
    setIsEnd: setIsEndProspect,
  } = useQueryProspect();

  useEffect(() => {
    if (globalProjectId) {
      setEnableProspect(true);
    } else {
      setEnableProspect(false);
      // Clear prospect when no project is selected
      dispatch(updateProspectId(""));
    }
  }, [globalProjectId, dispatch]);

  useEffect(() => {
    if (enableProspect && globalProjectId) {
      setSearchParamsProspect({
        ...searchParamsProspect,
        projectIds: [globalProjectId],
        keyword: undefined,
      });
      // Reset pagination when project changes
      setIsEndProspect(false);
    }
  }, [
    globalProjectId,
    enableProspect,
    setSearchParamsProspect,
    setIsEndProspect,
  ]);

  useEffect(() => {
    if (fillSearchParams) {
      const params = new URLSearchParams(querySearch);

      // Chỉ set param khi có giá trị
      if (globalProjectId) {
        params.set("projectId", String(globalProjectId));
      } else {
        params.delete("projectId");
      }

      if (globalProspectId) {
        params.set("prospectId", String(globalProspectId));
      } else {
        params.delete("prospectId");
      }

      // Delete params with DrillholeIds
      for (const key of params.keys()) {
        if (key.startsWith("DrillholeIds")) {
          params.delete(key);
        }
      }

      // Convert params to string and update router
      const paramString = params.toString();

      router.replace(`${window.location.pathname}?${paramString}`);
    }
  }, [globalProjectId, globalProspectId]);
  const isAdmin = useAppSelector(
    (state) => state.user.userInfo.roles
  )?.includes(UserRoles.Admin);
  if (isAdmin) return null;
  return (
    <div className="flex md:flex-row flex-col md:gap-5 gap-2 px-5 items-center ">
      <div className="flex gap-2 items-center">
        <p className="font-medium">Project</p>
        <Select
          size="large"
          value={globalProjectId}
          onChange={async (value) => {
            dispatch(updateProjectId(value));
          }}
          style={{ minWidth: 200 }}
          onSearch={(value) => {
            setSearchParamsProject({
              ...searchParamsProject,
              keyword: value,
            });
          }}
          searchValue={searchParamsProject?.keyword}
          onBlur={() => {
            setSearchParamsProject({
              ...searchParamsProject,
              keyword: "",
            });
          }}
          allowClear
          options={projects?.data?.items.map((project) => ({
            label: project.name,
            value: project.id,
          }))}
          notFoundContent={
            isLoadingProject ? <Spin size="small" /> : <>Not found</>
          }
          filterOption={false}
          showSearch
          placeholder="Select project"
          onPopupScroll={(event: any) => {
            handleScrollProject(event);
          }}
        />
      </div>
      <div className="flex gap-2 items-center">
        <p className="font-medium">Prospect</p>
        <Select
          size="large"
          style={{ minWidth: 200 }}
          value={globalProspectId}
          className="min-w-[400px]"
          disabled={!globalProjectId || isLoadingProspect}
          onClear={() => {
            dispatch(updateProspectId(""));
            const params = new URLSearchParams(querySearch);
            params.delete("prospectId");
            router.replace(`${window.location.pathname}?${params.toString()}`);
          }}
          onChange={async (value) => {
            dispatch(updateProspectId(value));
            dispatch(updateSelectedDrilhole(undefined));
          }}
          onSearch={(value) => {
            setSearchParamsProspect({
              ...searchParamsProspect,
              keyword: value,
            });
          }}
          searchValue={searchParamsProspect?.keyword}
          options={prospects?.data?.items.map((prospect) => ({
            label: prospect.name,
            value: prospect.id,
          }))}
          onBlur={() => {
            setSearchParamsProspect({
              ...searchParamsProspect,
              keyword: "",
            });
          }}
          notFoundContent={
            isLoadingProspect ? <Spin size="small" /> : <>Not found</>
          }
          filterOption={false} // disable client-side filtering
          showSearch
          placeholder={
            globalProjectId
              ? "Select prospect"
              : "Please select a project first"
          }
          onPopupScroll={(event: any) => {
            handleScrollProspect(event);
          }}
          allowClear
        />
      </div>
    </div>
  );
}

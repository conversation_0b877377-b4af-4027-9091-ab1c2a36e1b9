'use client';

import cn from 'classnames';
import './AppLongText.scss';
import { Fragment } from 'react';

interface Props {
  className?: string;
  children: string | Array<string> | undefined | number;
  maxLength?: number;
  maxLines?: number;
}

function AppLongText({ className = '', children = '', maxLength, maxLines }: Props) {
  if (!children) return <Fragment></Fragment>;
  let childrenString = children;
  if (typeof childrenString === 'number') childrenString = childrenString.toString();

  const textContent = Array.from(childrenString).join('');
  const truncateText = (input: string, maxLength?: number, maxLines?: number): string => {
    if (maxLines && maxLines <= 0) {
      return '';
    }

    let truncatedText = input;
    if (maxLength && input.length > maxLength) {
      truncatedText = input.substring(0, maxLength) + '...';
    }

    return truncatedText;
  };
  const truncatedText = truncateText(textContent, maxLength, maxLines);

  return (
    <div
      className={cn('overflow-hidden text-[14px] leading-[20px]', {
        [className]: className,
        'line-clamp-layer': maxLines,
      })}
      style={{ '--max-lines': maxLines } as React.CSSProperties}
    >
      {truncatedText}
    </div>
  );
}

export default AppLongText;

import { Tooltip } from 'antd';
import { TooltipPlacement } from 'antd/es/tooltip';
import React, { ReactNode } from 'react';
import { AiOutlineQuestionCircle } from 'react-icons/ai';

interface Props {
  hint: string | ReactNode;
  placement?: TooltipPlacement;
}

const AppGuide: React.FC<Props> = ({ hint, placement = 'right' }) => {
  return (
    <Tooltip title={hint} placement={placement}>
      <AiOutlineQuestionCircle />
    </Tooltip>
  );
};

export default AppGuide;

import { Select, SelectProps } from "antd";
import {
  Control,
  Controller,
  FieldValues,
  RegisterOptions,
} from "react-hook-form";

interface AppSelectProps extends Omit<SelectProps, "name"> {
  control: Control<FieldValues>;
  rules?:
    | Omit<
        RegisterOptions<FieldValues, any>,
        "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
      >
    | undefined;
  name: string;
  options: any[];
  onPopupScroll?: (event: any) => void;
}

const AppSelect = ({
  control,
  name,
  rules,
  options,
  onPopupScroll,
  ...selectProps
}: AppSelectProps) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { onChange, value, ...field } }) => (
        <Select
          {...field}
          {...selectProps}
          options={options}
          onPopupScroll={onPopupScroll}
          value={value === "" ? undefined : value}
          onChange={(newValue) => {
            onChange(newValue === undefined ? "" : newValue);
          }}
          onClear={() => {
            onChange("");
          }}
          defaultActiveFirstOption={false}
          autoClearSearchValue={true}
        />
      )}
    />
  );
};

export default AppSelect;

@tailwind base;
@tailwind components;
@tailwind utilities;
@font-face {
  font-family: "Visby";
  src: url("../../../../../public/fonts/VisbyRegular.woff") format("woff");
  font-weight: 400;
}

@font-face {
  font-family: "Visby";
  src: url("../../../../../public/fonts/VisbyBold.woff") format("woff");
  font-weight: 700;
  /* This font already custom for 0 and O */
}

body {
  font-family: "Visby" !important;
}

.ant-form-item {
  margin: 0 !important;
}

.ant-col label {
  font-weight: 500 !important;
}
.ant-menu-item {
  font-weight: 600 !important;
  font-size: 16px !important;
}
.ant-menu-light .ant-menu-item-selected {
  background-color: #2d669b !important;
  color: white !important;
}

.ant-menu-light .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: black !important;
}
.ant-table-row.ant-table-row-level-0 {
  border: 4px solid #2d669b !important;
}
.ant-table-cell {
  // border-bottom: 1px solid #d1d5db !important;
}
.ant-tabs-nav {
  margin: 0 !important;
}

.btn-purple {
  background: #65558f !important;
  color: #fff !important;
}

.upload-file .ant-upload {
  width: 100% !important;
}

/* table excel */
.table-excel div {
  overflow: auto;
  max-height: 55vh;
}
.ExcelTable {
  width: 100% !important;
}

.ExcelTable tr {
  border: 1px solid gray;
  border-bottom: none;
}

.ExcelTable tr:last-child {
  border-bottom: 1px solid gray;
}

.ExcelTable th,
.ExcelTable td {
  border-right: 1px solid gray;
}

.ExcelTable th:last-child,
.ExcelTable td:last-child {
  border-right: none;
}
.ant-table-cell {
  padding: 7px !important;
}

.ant-menu-title-content {
  font-weight: 600 !important;
  font-size: 16px !important;
}
.mobile {
  display: none;
}
@media (max-width: 768px) {
  .mobile {
    display: block;
  }
  .desktop {
    display: none;
  }
}

.desktop {
  display: block;
}
@media (min-width: 768px) {
  .mobile {
    display: none;
  }
  .desktop {
    display: block;
  }
}

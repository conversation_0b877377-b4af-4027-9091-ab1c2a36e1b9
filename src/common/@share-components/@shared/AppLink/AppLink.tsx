'use client';

import cn from 'classnames';
import Link from 'next/link';
import { ReactNode } from 'react';

interface Props {
  title: string | ReactNode;
  className?: string;
  href: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  isFullWidth?: boolean;
}

function AppLink({
  title = '',
  className = '',
  href = '',
  leftIcon,
  rightIcon,
  isFullWidth = false,
}: Props) {
  return (
    <Link
      href={href}
      className={cn(
        'flex items-center gap-[5px] text-[12px] text-black no-underline transition-all duration-150 ease-in-out hover:text-primary',
        {
          [className]: className,
        },
      )}
    >
      {leftIcon && <div className="left__icon">{leftIcon}</div>}
      <span
        className={cn('block', {
          'w-full': isFullWidth,
        })}
      >
        {title}
      </span>
      {rightIcon && <div className="left__icon">{rightIcon}</div>}
    </Link>
  );
}

export default AppLink;

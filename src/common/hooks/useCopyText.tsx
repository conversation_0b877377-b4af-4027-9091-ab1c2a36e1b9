import { notification } from 'antd';

function useCopyText() {
  const copyText = async (text, successMessage = 'Copied successfully') => {
    try {
      await navigator.clipboard.writeText(text);
      notification.success({
        message: successMessage,
      });
    } catch (error) {
      console.error('An error occurs', error);
      notification.error({
        message: 'An error occurs',
      });
    }
  };

  return copyText;
}

export default useCopyText;

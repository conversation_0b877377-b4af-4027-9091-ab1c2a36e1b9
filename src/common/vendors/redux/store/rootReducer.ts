import { accountSettingsSlice } from "@/modules/account-settings/redux/accountSettingsSlice";
import { apiKeyTypeSlice } from "@/modules/api-key-type/redux";
import { apiKeySlice } from "@/modules/api-key/redux/api-key.slice";
import { assaySuiteFieldSlice } from "@/modules/assay-suite-field/redux";
import { assayTranslationSlice } from "@/modules/assay-translation/redux";
import { assayAttributeSlice } from "@/modules/assay/redux";
import { assaySuiteSlice } from "@/modules/assay/redux/assay-suite.slice";
import { userSlice } from "@/modules/auth/redux/userSlice";
import { dhCalculationSlice } from "@/modules/dh-calculation/redux/dh-calculation.slice";
import { attributeSlice } from "@/modules/downhole-point/redux/attributeSlice";
import { suiteSlice } from "@/modules/downhole-point/redux/suiteSlice";
import { downholeSurveyTypeSlice } from "@/modules/downhole-survey-type/redux/downhole-survey-type.slice";
import { drillholesSlice } from "@/modules/drillhole/redux/drillholeSlice";
import { eventSlice } from "@/modules/export/redux/event-slice/event-slice";
import { exportTemplateSlice } from "@/modules/export/redux/export-template-slice";
import { geologyDatesSlice } from "@/modules/geology-date/redux";
import { geologyDescriptionsSlice } from "@/modules/geology-description/redux";
import { geologyFieldSlice } from "@/modules/geology-field/redux";
import { geologySuiteFieldSlice } from "@/modules/geology-suite-field/redux";
import { geologySuiteSlice } from "@/modules/geology-suite/redux";
import { geotechSuiteSlice } from "@/modules/geotech-suite/redux";
import { imageSubTypeSlice, imageTypeSlice } from "@/modules/image-type/redux";
import { imagesSlice } from "@/modules/image/redux/imageSlice";
import { imagesUploadSlice } from "@/modules/images-upload/redux/images-upload.slice";
import {
  ColoursSlice,
  NumberFieldsSlice,
  PickListSlice,
  UnitsSlice,
} from "@/modules/list/redux/list.slice";
import { loggingViewSlice } from "@/modules/logging-view/redux";
import { loggingSlice } from "@/modules/logging/redux/loggingSlice";
import { mappingTemplateSlice } from "@/modules/mapping-template/redux/mapping-template.slice";
import {
  numberRangeIntervalReducer,
  numberRangeReducer,
} from "@/modules/number-range/redux";
import { polygonSlice } from "@/modules/polygon/redux/polygonSlice";
import processImageSlice from "@/modules/process-images/redux/processImageSlice/process-image.slice";
import { projectsSlice } from "@/modules/projects/redux/projectSlice";
import { prospectsSlice } from "@/modules/prospect/redux/prospectSlice/prospect.slice";
import { rockGroupsSlice } from "@/modules/rock-groups/redux";
import { rockSelectNumberSlice } from "@/modules/rock-select-number/redux";
import { rockStylelice } from "@/modules/rock-style/redux/rock-style.slice";
import { rockTypeNumberSlice } from "@/modules/rock-type-number/redux";
import { rockTypelice } from "@/modules/rock-type/redux/rock-type.slice";
import { structureConditionSlice } from "@/modules/structure-condition/redux";
import { structureTypeSlice } from "@/modules/structure-type/redux";
import { structureSlice } from "@/modules/structure/redux/structure.slice";
import { tenantSlice } from "@/modules/tenant/redux";
import threedSlice from "@/modules/threed/redux/threedSlice/threed.slice";
import importDataSlice from "@/modules/upload-tools/redux/importDataSlice/import-data.slice";
import { userRoleConfigSlice } from "@/modules/user-role-config/redux";
import { workRoleSlice } from "@/modules/work-role/redux";
import { workflowSlice } from "@/modules/workflows/redux";
import { combineReducers } from "@reduxjs/toolkit";

const rootReducer = combineReducers({
  user: userSlice.reducer,
  accountSettings: accountSettingsSlice.reducer,
  project: projectsSlice.reducer,
  images: imagesSlice.reducer,
  attribute: attributeSlice.reducer,
  suite: suiteSlice.reducer,
  workflow: workflowSlice.reducer,
  polygon: polygonSlice.reducer,
  drillHole: drillholesSlice.reducer,
  tenant: tenantSlice.reducer,
  prospect: prospectsSlice.reducer,
  exportTemplate: exportTemplateSlice.reducer,
  event: eventSlice.reducer,
  rockStyle: rockStylelice.reducer,
  rockType: rockTypelice.reducer,
  logging: loggingSlice.reducer,
  assayAttribute: assayAttributeSlice.reducer,
  assaySuite: assaySuiteSlice.reducer,
  units: UnitsSlice.reducer,
  colours: ColoursSlice.reducer,
  pickLists: PickListSlice.reducer,
  geologySuiteField: geologySuiteFieldSlice.reducer,
  geologySuite: geologySuiteSlice.reducer,
  numberFields: NumberFieldsSlice.reducer,
  rockGroups: rockGroupsSlice.reducer,
  rockSelectNumber: rockSelectNumberSlice.reducer,
  geologyDates: geologyDatesSlice.reducer,
  geologyDescriptions: geologyDescriptionsSlice.reducer,
  rockTypeNumber: rockTypeNumberSlice.reducer,
  geologyField: geologyFieldSlice.reducer,
  userRoleConfig: userRoleConfigSlice.reducer,
  apiKey: apiKeySlice.reducer,
  workRole: workRoleSlice.reducer,
  apiKeyType: apiKeyTypeSlice.reducer,
  loggingView: loggingViewSlice.reducer,
  structure: structureSlice.reducer,
  structureType: structureTypeSlice.reducer,
  geotechSuite: geotechSuiteSlice.reducer,
  numberRange: numberRangeReducer,
  numberRangeIntervals: numberRangeIntervalReducer,
  structureCondition: structureConditionSlice.reducer,
  imagesUpload: imagesUploadSlice.reducer,
  dhCalculation: dhCalculationSlice.reducer,
  threeD: threedSlice.reducer,
  processImage: processImageSlice,
  downholeSurveyType: downholeSurveyTypeSlice.reducer,
  assayTranslation: assayTranslationSlice.reducer,
  assaySuiteField: assaySuiteFieldSlice.reducer,
  mappingTemplate: mappingTemplateSlice.reducer,
  importData: importDataSlice,
  imageType: imageTypeSlice.reducer,
  imageSubType: imageSubTypeSlice.reducer,
});

export default rootReducer;

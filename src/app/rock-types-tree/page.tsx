"use client";

import React, { useState } from "react";
import { Card, Breadcrumb, Skeleton } from "antd";
import { HomeOutlined, ApartmentOutlined } from "@ant-design/icons";
import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import { RockTypesTree } from "@/modules/rock-types-tree";
import Link from "next/link";

export default function RockTypesTreePage() {
  const [loading, setLoading] = useState(false);

  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
      ]}
    >
      {loading ? (
        <Skeleton active paragraph={{ rows: 10 }} />
      ) : (
        <RockTypesTree />
      )}
    </PermissionProvider>
  );
}

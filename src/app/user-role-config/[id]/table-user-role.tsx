import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { Button<PERSON>ommon } from "@/components/common/button-common";
import { TableCommon } from "@/components/common/table-common";
import { IconSearch } from "@/components/icons";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";
import { useAddUserToRoleConfig } from "@/modules/user-role-config/hooks/useAddUserToRoleConfig";
import { useGetListUserByRole } from "@/modules/user-role-config/hooks/useGetListUserByRole";
import { useGetListUserExceptRole } from "@/modules/user-role-config/hooks/useGetListUserExceptRole";
import { useRemoveUserFromRoleConfig } from "@/modules/user-role-config/hooks/useRemoveUserFromRoleConfig";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  MinusOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { TableColumnsType, Tabs, Tag } from "antd";
import { useEffect, useState } from "react";
import useTableState from "./useTableState";
const CONSTANTS = {
  PAGE_SIZE: 10,
  TABS: {
    NOT_ENROLLED: "not-enrolled",
    ENROLLED: "enrolled",
  },
} as const;
export default function TableUserRole({
  userRoleConfigId,
}: {
  userRoleConfigId: number;
}) {
  const notEnrolledState = useTableState();
  const enrolledState = useTableState();
  const [activeTab, setActiveTab] = useState(CONSTANTS.TABS.NOT_ENROLLED);
  const {
    data: dataUserEnrolled,
    loading: loadingUserEnrolled,
    request: requestUserEnrolled,
    total: totalUserEnrolled,
  } = useGetListUserByRole();
  const {
    data: dataUserNotEnrolled,
    loading: loadingUserNotEnrolled,
    request: requestUserNotEnrolled,
    total: totalUserNotEnrolled,
  } = useGetListUserExceptRole();
  const {
    request: requestAddUserToRoleConfig,
    loading: loadingAddUserToRoleConfig,
  } = useAddUserToRoleConfig();
  const {
    request: requestRemoveUserFromRoleConfig,
    loading: loadingRemoveUserFromRoleConfig,
  } = useRemoveUserFromRoleConfig();

  const fetchData = () => {
    requestUserEnrolled({
      userRoleConfigId,
      page: enrolledState.page,
      pageSize: CONSTANTS.PAGE_SIZE,
      keyword: enrolledState.keyword,
    });
    requestUserNotEnrolled({
      userRoleConfigId,
      page: notEnrolledState.page,
      pageSize: CONSTANTS.PAGE_SIZE,
      keyword: notEnrolledState.keyword,
    });
  };
  useEffect(() => {
    fetchData();
  }, [
    userRoleConfigId,
    enrolledState.page,
    notEnrolledState.page,
    enrolledState.keyword,
    notEnrolledState.keyword,
  ]);

  const columns: TableColumnsType<AccountBodyType> = [
    {
      title: "Email",
      dataIndex: "emailAddress",
      key: "email",
    },
    {
      title: "Username",
      dataIndex: "userName",
      key: "userName",
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render: (status, record, index) => {
        return status ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        );
      },
    },
  ];
  const handleRemove = () => {
    requestRemoveUserFromRoleConfig(
      {
        userRoleConfigId,
        userIds: enrolledState.selectedRowKeys as number[],
      },
      () => {
        fetchData();
        enrolledState.resetState();
        notEnrolledState.resetState();
      }
    );
  };

  const handleAdd = () => {
    requestAddUserToRoleConfig(
      {
        userRoleConfigId,
        userIds: notEnrolledState.selectedRowKeys as number[],
      },
      () => {
        fetchData();
        enrolledState.resetState();
        notEnrolledState.resetState();
      }
    );
  };
  const fontSize = useAppSelector((state) => state.user.fontSize);
  const items = [
    {
      key: CONSTANTS.TABS.NOT_ENROLLED,
      label: "Not Enrolled",
      children: (
        <div className="flex flex-col gap-3">
          <div className="">
            <div className="flex justify-between gap-2">
              <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
                <IconSearch />
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full font-normal  outline-none text-primary placeholder:text-gray80"
                  onChange={(e) => {
                    notEnrolledState.setKeyword(e.target.value);
                    notEnrolledState.setPage(1);
                  }}
                  value={notEnrolledState.keyword}
                />
              </div>
            </div>
          </div>
          <TableCommon
            style={{
              fontSize: `${fontSize}px`,
            }}
            columns={columns as any}
            dataSource={dataUserNotEnrolled}
            loading={loadingUserNotEnrolled}
            rowSelection={{
              selectedRowKeys: notEnrolledState.selectedRowKeys,
              onChange: (newSelectedRowKeys: React.Key[]) => {
                notEnrolledState.setSelectedRowKeys(newSelectedRowKeys);
              },
            }}
            rowKey="id"
            footer={() => {
              return (
                <ButtonCommon
                  disabled={notEnrolledState.selectedRowKeys.length === 0}
                  loading={loadingAddUserToRoleConfig}
                  onClick={handleAdd}
                  className="btn btn-sm bg-primary text-white hover:bg-primary w-full"
                >
                  <PlusOutlined /> Add {notEnrolledState.selectedRowKeys.length}{" "}
                  users
                </ButtonCommon>
              );
            }}
            pagination={{
              pageSize: CONSTANTS.PAGE_SIZE,
              current: notEnrolledState.page,
              total: totalUserNotEnrolled,
              onChange: (page: number) => {
                notEnrolledState.setPage(page);
              },
            }}
          />
        </div>
      ),
    },
    {
      key: CONSTANTS.TABS.ENROLLED,
      label: "Enrolled",
      children: (
        <div className="flex flex-col gap-3">
          <div className="">
            <div className="flex justify-between gap-2">
              <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
                <IconSearch />
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full font-normal  outline-none text-primary placeholder:text-gray80"
                  onChange={(e) => {
                    enrolledState.setKeyword(e.target.value);
                    enrolledState.setPage(1);
                  }}
                  value={enrolledState.keyword}
                />
              </div>
            </div>
          </div>
          <TableCommon
            style={{
              fontSize: `${fontSize}px`,
            }}
            columns={columns as any}
            dataSource={dataUserEnrolled}
            loading={loadingUserEnrolled}
            rowKey="id"
            rowSelection={{
              selectedRowKeys: enrolledState.selectedRowKeys,
              onChange: (newSelectedRowKeys: React.Key[]) => {
                enrolledState.setSelectedRowKeys(newSelectedRowKeys);
              },
            }}
            pagination={{
              pageSize: CONSTANTS.PAGE_SIZE,
              current: enrolledState.page,
              total: totalUserEnrolled,
              onChange: (page: number) => {
                enrolledState.setPage(page);
              },
            }}
            footer={() => {
              return (
                <ButtonCommon
                  disabled={enrolledState.selectedRowKeys.length === 0}
                  loading={loadingRemoveUserFromRoleConfig}
                  onClick={handleRemove}
                  className="btn btn-sm bg-primary text-white hover:bg-primary w-full"
                >
                  <MinusOutlined /> Remove{" "}
                  {enrolledState.selectedRowKeys.length} users
                </ButtonCommon>
              );
            }}
          />
        </div>
      ),
    },
  ];

  return (
    <Tabs
      activeKey={activeTab}
      onChange={(key) => setActiveTab(key as any)}
      items={items}
      className="w-full"
    />
  );
}

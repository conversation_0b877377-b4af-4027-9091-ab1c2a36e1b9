import { IconSearch } from "@/components/icons";

export const SearchInput = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  return (
    <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
      <IconSearch />
      <input
        type="text"
        placeholder="Search"
        className="w-full font-normal outline-none text-primary placeholder:text-gray80"
        onChange={(e) => onChange(e.target.value)}
        value={value}
      />
    </div>
  );
};

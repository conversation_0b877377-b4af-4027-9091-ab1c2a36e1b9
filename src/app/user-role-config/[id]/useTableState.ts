import { Key } from "antd/es/table/interface";
import { useState } from "react";

const useTableState = (initialPage = 1) => {
  const [keyword, setKeyword] = useState("");
  const [page, setPage] = useState(initialPage);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const resetState = () => {
    setKeyword("");
    setPage(1);
    setSelectedRowKeys([]);
  };

  return {
    keyword,
    setKeyword,
    page,
    setPage,
    selectedRowKeys,
    setSelectedRowKeys,
    resetState,
  };
};

export default useTableState;

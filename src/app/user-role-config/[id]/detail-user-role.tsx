"use client";
import { But<PERSON><PERSON><PERSON>mon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { SelectCommon } from "@/components/common/select-common";
import { TextAreaCommon } from "@/components/common/textarea-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetDetailUserRoleConfig } from "@/modules/user-role-config/hooks/useGetDetailUserRoleConfig";
import { useUpdateUserRoleConfig } from "@/modules/user-role-config/hooks/useUpdateUserRoleConfig";
import { userRoleFunctionOptions } from "@/modules/user-role-config/model/schema/enum/user-role-config.enum,";
import {
  UserRoleConfigBody,
  UserRoleConfigBodyType,
} from "@/modules/user-role-config/model/schema/user-role-config.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, Radio, RadioChangeEvent, Tag } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import TableUserRole from "./table-user-role";

const options = [
  { label: "Details", value: "Details" },
  { label: "People", value: "People" },
];
const plainOptions = [
  "Settings",
  "Load",
  "Logging",
  "Process Prepare",
  "Process Advanced",
  "Process Batch",
];
export default function DetailUserRole({ id }: { id: string }) {
  const [labelValue, setLabelValue] = useState("Details");
  const onChange = ({ target: { value } }: RadioChangeEvent) => {
    setLabelValue(value);
  };
  const { data: userRoleConfig, request: requestGetDetailUserRoleConfig } =
    useGetDetailUserRoleConfig();
  const [checkedList, setCheckedList] = useState<any[] | undefined>();

  const getDetailUserRoleConfig = () => {
    requestGetDetailUserRoleConfig({ Id: id }, (res) => {
      setValue("name", res?.name);
      setValue("description", res?.description);
      setValue("isActive", res?.isActive);
      setValue("functions", res?.functions);
    });
  };
  useEffect(() => {
    getDetailUserRoleConfig();
  }, [id]);
  const { control, handleSubmit, setValue } = useForm<UserRoleConfigBodyType>({
    resolver: zodResolver(UserRoleConfigBody),
  });
  const {
    request: requestUpdateUserRoleConfig,
    loading: loadingUpdateUserRoleConfig,
  } = useUpdateUserRoleConfig();
  const onSubmit = (values: UserRoleConfigBodyType) => {
    let result: any[] = [];
    checkedList?.forEach((item) => {
      plainOptions.forEach((item2, index) => {
        if (item2 === item) {
          result.push(index + 1);
        }
      });
    });

    requestUpdateUserRoleConfig(
      {
        ...values,
        id,
      },
      () => {
        toast.success("Update user role config successfully");
        getDetailUserRoleConfig();
      }
    );
  };

  return (
    <div className="flex flex-col gap-2">
      <p className="text-xl font-bold uppercase">
        User Role{" "}
        <Tag className="p-2 mx-2 " style={{ fontSize: "1rem" }} color="blue">
          {userRoleConfig?.name}
        </Tag>
      </p>
      <Radio.Group
        options={options}
        onChange={onChange}
        value={labelValue}
        optionType="button"
        buttonStyle="solid"
      />
      {labelValue === "Details" && (
        <div>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type user role name here"
              control={control}
            />
            <TextAreaCommon
              control={control}
              name="description"
              label="Description"
              placeholder="Type description here"
            />
            <SelectCommon
              name="functions"
              label="Functions"
              placeholder="Select functions"
              options={userRoleFunctionOptions}
              mode="multiple"
              control={control}
            />
            <ToogleCommon control={control} name="isActive" label="Is Active" />
            <div className="flex justify-end gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingUpdateUserRoleConfig}
                className="btn btn-sm  hover:bg-primary-hover bg-primary text-white border-none"
              >
                Update
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
      {labelValue === "People" && (
        <div>
          <TableUserRole userRoleConfigId={Number(id)} />
        </div>
      )}
    </div>
  );
}

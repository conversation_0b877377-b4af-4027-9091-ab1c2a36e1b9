"use client";
import { AppImages } from "@/common/configs";
import Image from "next/image";
import Link from "next/link";

export default function ForgotPasswordSuccess() {
  return (
    <div className="flex justify-center items-center h-screen">
      <div className="flex flex-col justify-center items-center border p-10 border-gray-400 rounded-2xl bg-white">
        <div className="text-black-1 text-center text-34-34 font-bold pb-3 flex flex-col gap-2">
          <div className="flex justify-center">
            <Image alt="" src={AppImages.logo} width={200} height={200} />
          </div>
        </div>

        <div className="flex flex-col items-center gap-6 mt-4">
          <Image
            src="/images/mail-success.svg"
            alt="Email sent"
            width={120}
            height={120}
          />

          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Check Your Email
            </h2>
            <p className="text-gray-600 mb-6 max-w-sm">
              We have sent password reset instructions to your email address.
              Please check your inbox.
            </p>
          </div>

          <Link
            href="/login"
            className="btn bg-primary text-white px-8 py-2 rounded-lg hover:bg-primary-hover transition-colors"
          >
            Back to Login
          </Link>

          <p className="text-sm text-gray-500 mt-4">
            Didn't receive the email?{" "}
            <Link
              href="/forgot-password"
              className="text-primary hover:text-primary-hover"
            >
              Try again
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

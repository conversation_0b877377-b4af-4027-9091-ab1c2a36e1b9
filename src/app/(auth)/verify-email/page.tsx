"use client";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { AppImages } from "../../../common/configs";
import { RequestState } from "../../../common/configs/app.contants";
import authRequest from "../../../modules/auth/api/auth.api";

export default function VerifyEmail() {
  const searchParams = useSearchParams();
  const [verificationState, setVerificationState] = useState<{
    isLoading: boolean;
    isError: boolean;
    message: string;
    email: string;
  }>({
    isLoading: true,
    isError: false,
    message: "",
    email: searchParams.get("email") || "",
  });

  useEffect(() => {
    (async () => {
      let token = searchParams.get("token") || "";
      // Replace spaces with + since they were incorrectly converted
      token = token.replace(/ /g, "+");
      if (!token) {
        setVerificationState({
          isLoading: false,
          isError: true,
          message: `Verification token is missing for ${
            searchParams.get("email") || ""
          }`,
          email: searchParams.get("email") || "",
        });
        return;
      }

      try {
        const response = await authRequest.verifyEmail({ token });
        if (response.state === RequestState.success) {
          setVerificationState({
            isLoading: false,
            isError: false,
            message: `Email ${
              searchParams.get("email") || ""
            } verified successfully`,
            email: searchParams.get("email") || "",
          });
        } else {
          console.log("response: ", response);
          setVerificationState({
            isLoading: false,
            isError: true,
            message:
              response.message ||
              `Email verification failed for ${
                searchParams.get("email") || ""
              }`,
            email: searchParams.get("email") || "",
          });
        }
      } catch (error: any) {
        setVerificationState({
          isLoading: false,
          isError: true,
          message:
            error?.response?.data?.error?.message ||
            "An error occurred during email verification",
          email: searchParams.get("email") || "",
        });
      }
    })();
  }, []);

  return (
    <div className="flex justify-center items-center min-h-screen bg-[#f5f5f5]">
      <div className="flex flex-col justify-center items-center border p-10 border-gray-300 rounded-2xl bg-white shadow-sm">
        <div className="text-black text-center text-3xl font-bold pb-3 flex flex-col gap-2">
          <div className="flex justify-center">
            <Image alt="" src={AppImages.logo} width={200} height={200} />
          </div>
        </div>

        <div className="flex flex-col items-center gap-3 mt-4">
          <Image
            src={
              verificationState.isError
                ? "/images/ic_notfound.svg"
                : "/images/mail-success.svg"
            }
            alt="Verification status"
            width={120}
            height={120}
          />

          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {verificationState.isLoading
                ? "Verifying Email..."
                : verificationState.isError
                ? "Verification Failed"
                : "Email Verified"}
            </h2>
            <p className="text-gray-600 mb-2 w-[400px] text-center">
              {verificationState.isLoading
                ? `Please wait while we verify ${
                    searchParams.get("email") || ""
                  }...`
                : verificationState.message}
            </p>
          </div>

          <Link
            href="/login"
            className="btn bg-primary text-white px-8 py-2 rounded-lg hover:bg-primary-hover transition-colors"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
}

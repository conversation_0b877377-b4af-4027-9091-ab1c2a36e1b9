import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableProspect from "@/modules/prospect/components/table-prospect";

export default function Page() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <TableProspect />
    </PermissionProvider>
  );
}

"use client";
import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import MergeDrillhole from "@/modules/merge-drillhole/components/merge-drillhole";
import { Tabs, TabsProps } from "antd";

export default function Page() {
  const onChange = (key: string) => {
    console.log(key);
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Merge Drillhole",
      children: <MergeDrillhole />,
    },
    {
      key: "2",
      label: "Tab 2",
      children: "Content of Tab Pane 2",
    },
  ];
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <Tabs items={items} onChange={onChange} />
    </PermissionProvider>
  );
}

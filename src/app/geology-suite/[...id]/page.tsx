import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableGeologySuiteField from "@/modules/geology-suite-field/components/table-geology-suite-field";

export default function Page({ params }: { params: { id: string } }) {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <TableGeologySuiteField id={params.id} />
    </PermissionProvider>
  );
}

import AppLayout from "@/common/@share-components/@layout/AppLayout";
import { StoreProvider } from "@/common/vendors/redux/provider/StoreProvider";
import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import { Dashboard } from "@/modules/dashboard/components/dashboard";

export default function Home() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <StoreProvider>
        <AppLayout fillSearchParams={false}>
          <Dashboard />
        </AppLayout>
      </StoreProvider>
    </PermissionProvider>
  );
}

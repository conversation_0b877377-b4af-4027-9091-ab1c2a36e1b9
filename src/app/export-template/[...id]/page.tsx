"use client";
import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import { ExportTemplate } from "@/modules/export/components/export-template";
import { useParams } from "next/navigation";

export default function Page() {
  const params = useParams();
  return (
    <PermissionProvider permissions={[PERMISSIONS.Admin, PERMISSIONS.Company]}>
      <ExportTemplate id={params.id[0]} />
    </PermissionProvider>
  );
}
